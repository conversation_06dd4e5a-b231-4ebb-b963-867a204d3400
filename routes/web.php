<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HomeSliderController;
use App\Http\Controllers\Admin\AboutPreviewController;
use App\Http\Controllers\Admin\ServicesPreviewController;
use App\Http\Controllers\Admin\FeaturedProjectsController;
use App\Http\Controllers\Admin\TestimonialsSectionController;
use App\Http\Controllers\Admin\ContactCtaController;
use App\Http\Controllers\Admin\AboutUsController;
use App\Http\Controllers\Admin\ProjectController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\FounderController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\ProfileController;


Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/projects', [HomeController::class, 'projects'])->name('projects');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/about-us', [HomeController::class, 'aboutUs'])->name('about-us');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog');
Route::get('/media-center', [HomeController::class, 'mediaCenter'])->name('media-center');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/project/{slug}', [HomeController::class, 'projectDetails'])->name('project-details');
Route::get('/blog/{slug}', [HomeController::class, 'blogDetails'])->name('blog-details');

// Contact form submission
Route::post('/contact/submit', [HomeController::class, 'submitContact'])->name('contact.submit');


// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect /admin to login if not authenticated, to dashboard if authenticated
    Route::get('/', function () {
        return auth('admin')->check() ? redirect()->route('admin.dashboard') : redirect()->route('admin.login');
    });

    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');

    // Admin Protected Routes
    Route::middleware('auth:admin')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

        // Admin Menu Routes
        Route::resource('sliders', HomeSliderController::class)->parameters(['sliders' => 'slider']);

        // Home Page Section Management
        Route::get('about-preview', [AboutPreviewController::class, 'index'])->name('about-preview.index');
        Route::get('about-preview/edit', [AboutPreviewController::class, 'edit'])->name('about-preview.edit');
        Route::put('about-preview', [AboutPreviewController::class, 'update'])->name('about-preview.update');
        Route::post('about-preview/toggle-status', [AboutPreviewController::class, 'toggleStatus'])->name('about-preview.toggle-status');

        Route::get('why-choose-us', [\App\Http\Controllers\Admin\WhyChooseUsController::class, 'index'])->name('why-choose-us.index');
        Route::get('why-choose-us/edit', [\App\Http\Controllers\Admin\WhyChooseUsController::class, 'edit'])->name('why-choose-us.edit');
        Route::put('why-choose-us', [\App\Http\Controllers\Admin\WhyChooseUsController::class, 'update'])->name('why-choose-us.update');
        Route::post('why-choose-us/toggle-status', [\App\Http\Controllers\Admin\WhyChooseUsController::class, 'toggleStatus'])->name('why-choose-us.toggle-status');

        Route::get('services-preview', [ServicesPreviewController::class, 'index'])->name('services-preview.index');
        Route::get('services-preview/edit', [ServicesPreviewController::class, 'edit'])->name('services-preview.edit');
        Route::put('services-preview', [ServicesPreviewController::class, 'update'])->name('services-preview.update');
        Route::post('services-preview/toggle-status', [ServicesPreviewController::class, 'toggleStatus'])->name('services-preview.toggle-status');

        Route::get('featured-projects', [FeaturedProjectsController::class, 'index'])->name('featured-projects.index');
        Route::get('featured-projects/edit', [FeaturedProjectsController::class, 'edit'])->name('featured-projects.edit');
        Route::put('featured-projects', [FeaturedProjectsController::class, 'update'])->name('featured-projects.update');
        Route::post('featured-projects/toggle-status', [FeaturedProjectsController::class, 'toggleStatus'])->name('featured-projects.toggle-status');

        Route::get('testimonials-section', [TestimonialsSectionController::class, 'index'])->name('testimonials-section.index');
        Route::get('testimonials-section/edit', [TestimonialsSectionController::class, 'edit'])->name('testimonials-section.edit');
        Route::put('testimonials-section', [TestimonialsSectionController::class, 'update'])->name('testimonials-section.update');
        Route::post('testimonials-section/toggle-status', [TestimonialsSectionController::class, 'toggleStatus'])->name('testimonials-section.toggle-status');

        Route::get('contact-cta', [ContactCtaController::class, 'index'])->name('contact-cta.index');
        Route::get('contact-cta/edit', [ContactCtaController::class, 'edit'])->name('contact-cta.edit');
        Route::put('contact-cta', [ContactCtaController::class, 'update'])->name('contact-cta.update');
        Route::post('contact-cta/toggle-status', [ContactCtaController::class, 'toggleStatus'])->name('contact-cta.toggle-status');
        Route::resource('about-us', AboutUsController::class)->parameters(['about-us' => 'aboutUsSection']);
        Route::resource('projects', ProjectController::class);
        Route::resource('services', ServiceController::class);
        Route::get('contact', [ContactController::class, 'index'])->name('contact.index');
        Route::get('contact/enquiry/{enquiry}', [ContactController::class, 'showEnquiry'])->name('contact.enquiry.show');
        Route::patch('contact/enquiry/{enquiry}/status', [ContactController::class, 'updateEnquiryStatus'])->name('contact.enquiry.update-status');
        Route::delete('contact/enquiry/{enquiry}', [ContactController::class, 'deleteEnquiry'])->name('contact.enquiry.delete');
        Route::get('contact/visit/{visit}', [ContactController::class, 'showVisit'])->name('contact.visit.show');
        Route::patch('contact/visit/{visit}/status', [ContactController::class, 'updateVisitStatus'])->name('contact.visit.update-status');
        Route::delete('contact/visit/{visit}', [ContactController::class, 'deleteVisit'])->name('contact.visit.delete');
        Route::resource('testimonials', TestimonialController::class);
        Route::resource('founders', FounderController::class);
        Route::resource('blogs', BlogController::class);
        Route::resource('settings', SettingsController::class);
        Route::put('settings/bulk-update', [SettingsController::class, 'update'])->name('settings.bulk-update');
        Route::post('settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');

        // Profile routes
        Route::get('profile', [ProfileController::class, 'show'])->name('profile.show');
        Route::get('profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::put('profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::get('profile/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('profile.change-password');
        Route::put('profile/change-password', [ProfileController::class, 'updatePassword'])->name('profile.change-password.update');
        Route::delete('profile/avatar', [ProfileController::class, 'removeAvatar'])->name('profile.remove-avatar');
    });
});

// Add named login route for Laravel's default auth system
Route::get('/login', function () {
    return redirect()->route('admin.login');
})->name('login');
