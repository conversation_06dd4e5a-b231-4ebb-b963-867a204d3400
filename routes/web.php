<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HomeSliderController;
use App\Http\Controllers\Admin\HomeContentController;
use App\Http\Controllers\Admin\AboutUsController;
use App\Http\Controllers\Admin\ProjectController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\SettingsController;


Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/projects', [HomeController::class, 'projects'])->name('projects');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/about-us', [HomeController::class, 'aboutUs'])->name('about-us');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog');
Route::get('/media-center', [HomeController::class, 'mediaCenter'])->name('media-center');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/project/{slug}', [HomeController::class, 'projectDetails'])->name('project-details');
Route::get('/blog/{slug}', [HomeController::class, 'blogDetails'])->name('blog-details');


// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect /admin to login if not authenticated, to dashboard if authenticated
    Route::get('/', function () {
        return auth()->check() ? redirect()->route('admin.dashboard') : redirect()->route('admin.login');
    });

    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');

    // Admin Protected Routes
    Route::middleware('auth')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

        // Admin Menu Routes
        Route::resource('sliders', HomeSliderController::class)->parameters(['sliders' => 'slider']);
        Route::resource('home-content', HomeContentController::class);
        Route::resource('about-us', AboutUsController::class)->parameters(['about-us' => 'aboutUsSection']);
        Route::resource('projects', ProjectController::class);
        Route::resource('services', ServiceController::class);
        Route::resource('contact', ContactController::class);
        Route::resource('blogs', BlogController::class);
        Route::resource('settings', SettingsController::class);
        Route::put('settings/bulk-update', [SettingsController::class, 'update'])->name('settings.bulk-update');
        Route::post('settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');

        // Profile routes
        Route::get('profile', [AuthController::class, 'showProfile'])->name('profile.show');
        Route::get('profile/edit', [AuthController::class, 'profile'])->name('profile.edit');
        Route::put('profile', [AuthController::class, 'updateProfile'])->name('profile.update');
    });
});

// Add named login route for Laravel's default auth system
Route::get('/login', function () {
    return redirect()->route('admin.login');
})->name('login');
