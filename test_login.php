<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Hash;

// Test admin login credentials
$email = '<EMAIL>';
$password = 'password123';

// Create Laravel app instance
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test password verification
$admin = App\Models\Admin::where('email', $email)->first();

if ($admin) {
    echo "Admin found: " . $admin->name . "\n";
    echo "Email: " . $admin->email . "\n";
    echo "Role: " . $admin->role . "\n";
    echo "Status: " . $admin->status . "\n";
    
    $passwordCheck = Hash::check($password, $admin->password);
    echo "Password check: " . ($passwordCheck ? 'PASS' : 'FAIL') . "\n";
    
    $isActive = $admin->isActive();
    echo "Is Active: " . ($isActive ? 'YES' : 'NO') . "\n";
} else {
    echo "Admin not found!\n";
}
