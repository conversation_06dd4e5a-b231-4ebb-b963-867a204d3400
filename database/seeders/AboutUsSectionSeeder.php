<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AboutUsSection;

class AboutUsSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sections = [
            [
                'section_key' => 'who_we_are',
                'title' => 'Who We Are',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'description' => '<p>Hestia Abodes is a premium real estate consultancy firm dedicated to transforming your property dreams into reality. With years of experience in the industry, we specialize in providing comprehensive real estate solutions that cater to both residential and commercial needs.</p><p>Our team of seasoned professionals brings together expertise in property development, investment advisory, and market analysis to ensure our clients make informed decisions. We believe in building lasting relationships based on trust, transparency, and exceptional service delivery.</p>',
                'content_type' => 'image',
                'icon_class' => null,
                'image_path' => 'about-us/who-we-are.jpg',
                'image_position' => 'right',
                'columns_count' => 1,
                'subcontent' => null,
                'background_type' => 'none',
                'background_color' => null,
                'background_image' => null,
                'icon_color' => '#ffffff',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'section_key' => 'our_expertise',
                'title' => 'Our Expertise',
                'subtitle' => 'Comprehensive Real Estate Solutions',
                'description' => '<p>We offer a wide range of services designed to meet all your real estate needs:</p>',
                'content_type' => 'text',
                'icon_class' => null,
                'image_path' => null,
                'image_position' => 'left',
                'columns_count' => 3,
                'subcontent' => [
                    [
                        'title' => 'Property Development',
                        'description' => 'From concept to completion, we manage every aspect of property development with precision and attention to detail.',
                        'icon_class' => 'fas fa-building'
                    ],
                    [
                        'title' => 'Investment Advisory',
                        'description' => 'Strategic guidance to help you make profitable real estate investments with comprehensive market analysis.',
                        'icon_class' => 'fas fa-chart-line'
                    ],
                    [
                        'title' => 'Property Management',
                        'description' => 'Complete property management services to maximize your investment returns and maintain property value.',
                        'icon_class' => 'fas fa-key'
                    ]
                ],
                'background_type' => 'color',
                'background_color' => '#f8f9fa',
                'background_image' => null,
                'icon_color' => '#007bff',
                'sort_order' => 2,
                'is_active' => true,
            ],

            [
                'section_key' => 'mission_vision',
                'title' => 'Our Mission & Vision',
                'subtitle' => 'Empowering Property Dreams & Building Tomorrow\'s Communities',
                'description' => '<p>Our mission and vision guide everything we do at Hestia Abodes:</p>',
                'content_type' => 'text',
                'icon_class' => null,
                'image_path' => null,
                'image_position' => 'left',
                'columns_count' => 2,
                'subcontent' => [
                    [
                        'title' => 'Our Mission',
                        'description' => 'To provide exceptional real estate services that exceed client expectations while contributing to sustainable community development. We strive to be the most trusted name in real estate by delivering innovative solutions and maintaining the highest standards of professionalism.',
                        'icon_class' => 'fas fa-bullseye'
                    ],
                    [
                        'title' => 'Our Vision',
                        'description' => 'To be the leading real estate consultancy firm that shapes the future of property development through innovation, sustainability, and client-centric solutions. We envision creating spaces that enhance quality of life and contribute to thriving communities.',
                        'icon_class' => 'fas fa-eye'
                    ]
                ],
                'background_type' => 'none',
                'background_color' => null,
                'background_image' => null,
                'icon_color' => '#28a745',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'section_key' => 'what_sets_us_apart',
                'title' => 'What Sets Us Apart',
                'subtitle' => 'Excellence in Every Detail',
                'description' => '<p>Our commitment to excellence is reflected in every project we undertake. We combine industry expertise with innovative approaches to deliver results that exceed expectations. Our personalized service ensures that each client receives tailored solutions that meet their unique requirements.</p>',
                'content_type' => 'icon',
                'icon_class' => 'fas fa-star',
                'image_path' => null,
                'image_position' => 'left',
                'columns_count' => 1,
                'subcontent' => null,
                'sort_order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($sections as $section) {
            AboutUsSection::updateOrCreate(
                ['section_key' => $section['section_key']],
                $section
            );
        }
    }
}
