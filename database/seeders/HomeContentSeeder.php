<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeContent;

class HomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        HomeContent::updateOrCreate(
            ['section_name' => 'hero_section'],
            [
                'item_type' => 'hero',
                'title' => 'Welcome to Hestia Abodes',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'content' => '<p>Discover premium residential and commercial properties with Hestia Abodes. We provide comprehensive real estate solutions tailored to your needs.</p>',
                'button_text' => 'Explore Properties',
                'button_url' => '/projects',
                'sort_order' => 1,
                'is_active' => true,
                'image' => 'home-content/hero-bg.jpg',
                'images' => ['home-content/hero-bg.jpg'],
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'about_preview'],
            [
                'item_type' => 'about',
                'title' => 'Why Choose Hestia Abodes?',
                'subtitle' => 'Excellence in Real Estate Services',
                'content' => '<p>With years of experience in the real estate industry, we bring expertise, transparency, and personalized service to every transaction. Our team is dedicated to helping you find the perfect property or investment opportunity.</p>',
                'image' => 'home-content/about-preview.jpg',
                'images' => ['home-content/about-preview.jpg'],
                'button_text' => 'Learn More',
                'button_url' => '/about-us',
                'sort_order' => 2,
                'is_active' => true,
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'services_preview'],
            [
                'item_type' => 'services',
                'title' => 'Our Services',
                'subtitle' => 'Comprehensive Real Estate Solutions',
                'content' => '<p>From property buying and selling to investment advisory and legal support, we offer a complete range of real estate services to meet all your property needs.</p>',
                'image' => 'home-content/services-preview.jpg',
                'images' => ['home-content/services-preview.jpg'],
                'button_text' => 'View All Services',
                'button_url' => '/services',
                'sort_order' => 3,
                'is_active' => true,
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'why_choose_us'],
            [
                'item_type' => 'features',
                'title' => 'Why Choose Hestia Abodes?',
                'subtitle' => 'Where every decision is backed by trust',
                'content' => '<p>We bring expertise, transparency, and personalized service to every transaction.</p>',
                'sort_order' => 3,
                'is_active' => true,
                'additional_data' => [
                    'features' => [
                        [
                            'icon' => 'fas fa-search',
                            'icon_color' => '#D4AF37',
                            'title' => 'Selective, Not Generic',
                            'description' => 'We only recommend what fits you. Our curated approach ensures you see properties that truly match your needs and preferences.'
                        ],
                        [
                            'icon' => 'fas fa-users',
                            'icon_color' => '#D4AF37',
                            'title' => 'We Represent You, Not the Developer',
                            'description' => 'Your goals come first. As independent consultants, we provide unbiased advice focused on your best interests.'
                        ],
                        [
                            'icon' => 'fas fa-chart-line',
                            'icon_color' => '#D4AF37',
                            'title' => 'Market Insight Over Marketing',
                            'description' => 'We share context, not just content. Get real market analysis and honest feedback, not sales pitches.'
                        ],
                        [
                            'icon' => 'fas fa-list-alt',
                            'icon_color' => '#D4AF37',
                            'title' => 'Process-Driven',
                            'description' => 'You always know what\'s happening next. Our structured approach keeps you informed at every step.'
                        ],
                        [
                            'icon' => 'fas fa-shield-alt',
                            'icon_color' => '#D4AF37',
                            'title' => 'Compliance-First',
                            'description' => 'We protect your investment with legal clarity. All transactions are handled with complete regulatory compliance.'
                        ],
                        [
                            'icon' => 'fas fa-handshake',
                            'icon_color' => '#D4AF37',
                            'title' => 'Here for the Long Haul',
                            'description' => 'Even after deal closure, we remain your advisors. Our relationship continues beyond the transaction.'
                        ]
                    ]
                ]
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'featured_projects'],
            [
                'item_type' => 'projects',
                'title' => 'Featured Projects',
                'subtitle' => 'Handpicked Premium Properties',
                'content' => '<p>Explore our carefully selected portfolio of premium residential and commercial projects across Pune and surrounding areas.</p>',
                'button_text' => 'View All Projects',
                'button_url' => '/projects',
                'sort_order' => 4,
                'is_active' => true,
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'testimonials_section'],
            [
                'item_type' => 'testimonials',
                'title' => 'What Our Clients Say',
                'subtitle' => 'Trusted by Hundreds of Happy Clients',
                'content' => '<p>Read what our satisfied clients have to say about their experience with Hestia Abodes and how we helped them achieve their real estate goals.</p>',
                'sort_order' => 5,
                'is_active' => true,
            ]
        );

        HomeContent::updateOrCreate(
            ['section_name' => 'contact_cta'],
            [
                'item_type' => 'cta',
                'title' => 'Ready to Find Your Dream Property?',
                'subtitle' => 'Get in Touch with Our Experts',
                'content' => '<p>Contact our experienced team today for personalized real estate solutions. We\'re here to help you every step of the way.</p>',
                'button_text' => 'Contact Us',
                'button_url' => '/contact',
                'sort_order' => 6,
                'is_active' => true,
            ]
        );
    }
}
