<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeContent;

class HomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        HomeContent::updateOrCreate(
            ['section' => 'hero_section'],
            [
                'title' => 'Welcome to Hestia Abodes',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'description' => '<p>Discover premium residential and commercial properties with Hestia Abodes. We provide comprehensive real estate solutions tailored to your needs.</p>',
                'button_text' => 'Explore Properties',
                'button_link' => '/projects',
                'sort_order' => 1,
                'is_active' => true,
                'item_type' => 'hero',
                'image' => 'home-content/hero-bg.jpg',
            ]
        );

        HomeContent::updateOrCreate(
            ['section' => 'about_preview'],
            [
                'title' => 'Why Choose Hestia Abodes?',
                'subtitle' => 'Excellence in Real Estate Services',
                'description' => '<p>With years of experience in the real estate industry, we bring expertise, transparency, and personalized service to every transaction. Our team is dedicated to helping you find the perfect property or investment opportunity.</p>',
                'image' => 'home-content/about-preview.jpg',
                'button_text' => 'Learn More',
                'button_link' => '/about-us',
                'sort_order' => 2,
                'is_active' => true,
                'item_type' => 'about',
            ]
        );

        HomeContent::updateOrCreate(
            ['section' => 'services_preview'],
            [
                'title' => 'Our Services',
                'subtitle' => 'Comprehensive Real Estate Solutions',
                'description' => '<p>From property buying and selling to investment advisory and legal support, we offer a complete range of real estate services to meet all your property needs.</p>',
                'button_text' => 'View All Services',
                'button_link' => '/services',
                'sort_order' => 3,
                'is_active' => true,
                'item_type' => 'services',
            ]
        );

        HomeContent::updateOrCreate(
            ['section' => 'featured_projects'],
            [
                'title' => 'Featured Projects',
                'subtitle' => 'Handpicked Premium Properties',
                'description' => '<p>Explore our carefully selected portfolio of premium residential and commercial projects across Pune and surrounding areas.</p>',
                'button_text' => 'View All Projects',
                'button_link' => '/projects',
                'sort_order' => 4,
                'is_active' => true,
                'item_type' => 'projects',
            ]
        );

        HomeContent::updateOrCreate(
            ['section' => 'testimonials_section'],
            [
                'title' => 'What Our Clients Say',
                'subtitle' => 'Trusted by Hundreds of Happy Clients',
                'description' => '<p>Read what our satisfied clients have to say about their experience with Hestia Abodes and how we helped them achieve their real estate goals.</p>',
                'sort_order' => 5,
                'is_active' => true,
                'item_type' => 'testimonials',
            ]
        );

        HomeContent::updateOrCreate(
            ['section' => 'contact_cta'],
            [
                'title' => 'Ready to Find Your Dream Property?',
                'subtitle' => 'Get in Touch with Our Experts',
                'description' => '<p>Contact our experienced team today for personalized real estate solutions. We\'re here to help you every step of the way.</p>',
                'button_text' => 'Contact Us',
                'button_link' => '/contact',
                'sort_order' => 6,
                'is_active' => true,
                'item_type' => 'cta',
            ]
        );
    }
}
