<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Service;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'title' => 'Property Buying Assistance',
                'description' => '<p>Expert guidance throughout your property buying journey. We help you find the perfect property that matches your requirements and budget, with complete legal verification and documentation support.</p><ul><li>Property search and shortlisting</li><li>Site visits and evaluation</li><li>Legal documentation verification</li><li>Negotiation support</li><li>Registration assistance</li></ul>',
                'background_type' => 'color',
                'background_color' => '#f8f9fa',
                'icon_color' => '#cfaa13',
            ],
            [
                'title' => 'Property Selling Services',
                'description' => '<p>Maximize your property value with our comprehensive selling services. We provide market analysis, professional marketing, and end-to-end transaction management.</p><ul><li>Property valuation and pricing</li><li>Professional photography and marketing</li><li>Buyer screening and qualification</li><li>Negotiation and closing support</li><li>Legal documentation assistance</li></ul>',
                'background_type' => 'none',
                'icon_color' => '#cfaa13',
            ],
            [
                'title' => 'Investment Advisory',
                'description' => '<p>Make informed investment decisions with our expert advisory services. We analyze market trends, evaluate investment potential, and provide strategic guidance for your real estate portfolio.</p><ul><li>Market research and analysis</li><li>Investment opportunity identification</li><li>ROI calculations and projections</li><li>Portfolio diversification strategies</li><li>Risk assessment and mitigation</li></ul>',
                'background_type' => 'color',
                'background_color' => '#f5f5f5',
                'icon_color' => '#cfaa13',
            ],
            [
                'title' => 'Legal & Documentation',
                'description' => '<p>Ensure secure transactions with our comprehensive legal and documentation services. We handle all legal aspects of property transactions to protect your interests.</p><ul><li>Title verification and clearance</li><li>Agreement drafting and review</li><li>Registration and stamp duty</li><li>NOC and approvals</li><li>Dispute resolution support</li></ul>',
                'background_type' => 'none',
                'icon_color' => '#cfaa13',
            ],
            [
                'title' => 'Property Management',
                'description' => '<p>Professional property management services for landlords and investors. We handle tenant management, maintenance, and maximize your rental income.</p><ul><li>Tenant screening and placement</li><li>Rent collection and management</li><li>Property maintenance and repairs</li><li>Legal compliance and documentation</li><li>Regular property inspections</li></ul>',
                'background_type' => 'color',
                'background_color' => '#f8f9fa',
                'icon_color' => '#cfaa13',
            ],
            [
                'title' => 'Builder Partnerships',
                'description' => '<p>Exclusive mandate services for builders and developers. We provide dedicated marketing, sales support, and strategic guidance to maximize your project success.</p><ul><li>Exclusive marketing mandates</li><li>Sales team training and support</li><li>Digital marketing and promotion</li><li>Customer relationship management</li><li>Market feedback and insights</li></ul>',
                'background_type' => 'none',
                'icon_color' => '#cfaa13',
            ],
        ];

        foreach ($services as $service) {
            Service::updateOrCreate(
                ['title' => $service['title']],
                $service
            );
        }
    }
}
