<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\VisitSchedule;
use Carbon\Carbon;

class VisitScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $visits = [
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543210',
                'property_type' => '2BHK Apartment',
                'location_preference' => 'Baner, Pune',
                'budget_range' => '70-80 Lakhs',
                'preferred_date' => Carbon::tomorrow(),
                'preferred_time' => '10:00:00',
                'alternate_date' => Carbon::tomorrow()->addDay(),
                'alternate_time' => '14:00:00',
                'special_requirements' => 'Need parking space for 2 cars',
                'status' => 'confirmed',
                'confirmed_at' => Carbon::now()->subHours(2),
                'scheduled_date' => Carbon::tomorrow(),
                'scheduled_time' => '10:00:00',
                'visit_type' => 'property_viewing',
                'created_at' => Carbon::now()->subDays(2),
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 8765432109',
                'property_type' => '3BHK Villa',
                'location_preference' => 'Wakad, Pune',
                'budget_range' => '1.2-1.5 Crores',
                'preferred_date' => Carbon::today()->addDays(3),
                'preferred_time' => '11:00:00',
                'special_requirements' => 'Prefer ground floor, need garden space',
                'status' => 'pending',
                'visit_type' => 'property_viewing',
                'created_at' => Carbon::now()->subDay(),
            ],
            [
                'name' => 'Suresh Patel',
                'email' => '<EMAIL>',
                'phone' => '+91 7654321098',
                'property_type' => 'Commercial Office',
                'location_preference' => 'Hinjewadi, Pune',
                'budget_range' => '50-60 Lakhs',
                'preferred_date' => Carbon::today()->addDays(2),
                'preferred_time' => '15:00:00',
                'alternate_date' => Carbon::today()->addDays(4),
                'alternate_time' => '16:00:00',
                'special_requirements' => 'Need 24/7 security and power backup',
                'status' => 'confirmed',
                'confirmed_at' => Carbon::now()->subHours(4),
                'scheduled_date' => Carbon::today()->addDays(2),
                'scheduled_time' => '15:00:00',
                'visit_type' => 'property_viewing',
                'agent_notes' => 'Client is serious buyer, ready for immediate purchase',
                'created_at' => Carbon::now()->subDays(3),
            ],
            [
                'name' => 'Neha Gupta',
                'email' => '<EMAIL>',
                'phone' => '+91 6543210987',
                'property_type' => '1BHK Apartment',
                'location_preference' => 'Kothrud, Pune',
                'budget_range' => '45-50 Lakhs',
                'preferred_date' => Carbon::yesterday(),
                'preferred_time' => '14:00:00',
                'status' => 'completed',
                'scheduled_date' => Carbon::yesterday(),
                'scheduled_time' => '14:00:00',
                'visit_type' => 'property_viewing',
                'agent_notes' => 'Visit completed successfully. Client liked the property and considering purchase.',
                'created_at' => Carbon::now()->subDays(5),
            ],
            [
                'name' => 'Arjun Reddy',
                'email' => '<EMAIL>',
                'phone' => '+91 5432109876',
                'property_type' => 'Investment Consultation',
                'location_preference' => 'Multiple locations',
                'budget_range' => '2-3 Crores',
                'preferred_date' => Carbon::today()->addWeek(),
                'preferred_time' => '10:00:00',
                'special_requirements' => 'Need detailed ROI analysis and market trends',
                'status' => 'pending',
                'visit_type' => 'consultation',
                'created_at' => Carbon::now()->subHours(6),
            ],
            [
                'name' => 'Meera Joshi',
                'email' => '<EMAIL>',
                'phone' => '+91 4321098765',
                'property_type' => 'Plot/Land',
                'location_preference' => 'Talegaon, Pune',
                'budget_range' => '30-40 Lakhs',
                'preferred_date' => Carbon::today()->addDays(5),
                'preferred_time' => '09:00:00',
                'alternate_date' => Carbon::today()->addDays(6),
                'alternate_time' => '09:00:00',
                'special_requirements' => 'Need clear title and RERA approved plot',
                'status' => 'rescheduled',
                'visit_type' => 'site_visit',
                'agent_notes' => 'Original date was not suitable for client, rescheduled to next week',
                'created_at' => Carbon::now()->subDays(4),
            ],
        ];

        foreach ($visits as $visit) {
            VisitSchedule::create($visit);
        }
    }
}
