<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
            ]
        );

        // Run admin and project seeders
        $this->call([
            AdminSeeder::class,
            ProjectSeeder::class,
            SliderSeeder::class,
            HomeContentSeeder::class,
            AboutUsSectionSeeder::class,
            ServiceSeeder::class,
            ContactSectionSeeder::class,
            BlogSeeder::class,
            TestimonialSeeder::class,
            SettingsSeeder::class,
        ]);
    }
}
