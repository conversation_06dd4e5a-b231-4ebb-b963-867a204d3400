<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ContactEnquiry;
use Carbon\Carbon;

class ContactEnquirySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $enquiries = [
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543210',
                'subject' => 'Property Investment Inquiry',
                'message' => 'I am interested in investing in residential properties in Pune. Could you please provide information about upcoming projects and expected ROI?',
                'enquiry_type' => 'investment',
                'preferred_contact' => 'phone',
                'status' => 'new',
                'source' => 'website',
                'created_at' => Carbon::now()->subDays(2),
            ],
            [
                'name' => '<PERSON>riya <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+91 8765432109',
                'subject' => '2BHK Apartment Search',
                'message' => 'Looking for a 2BHK apartment in Baner or Wakad area. Budget around 80-90 lakhs. Please share available options.',
                'enquiry_type' => 'property',
                'preferred_contact' => 'email',
                'status' => 'contacted',
                'contacted_at' => Carbon::now()->subDay(),
                'source' => 'website',
                'created_at' => Carbon::now()->subDays(3),
            ],
            [
                'name' => 'Amit Patel',
                'email' => '<EMAIL>',
                'phone' => '+91 7654321098',
                'subject' => 'Commercial Space Requirement',
                'message' => 'Need office space for IT company, around 3000-4000 sq ft in Hinjewadi or Magarpatta. Please contact.',
                'enquiry_type' => 'property',
                'preferred_contact' => 'both',
                'status' => 'in_progress',
                'contacted_at' => Carbon::now()->subHours(6),
                'source' => 'website',
                'created_at' => Carbon::now()->subDays(1),
            ],
            [
                'name' => 'Sneha Desai',
                'email' => '<EMAIL>',
                'phone' => '+91 6543210987',
                'subject' => 'First Time Home Buyer',
                'message' => 'Hi, I am a first-time home buyer. Need guidance on home loan process and property selection. Please help.',
                'enquiry_type' => 'general',
                'preferred_contact' => 'email',
                'status' => 'resolved',
                'contacted_at' => Carbon::now()->subDays(5),
                'source' => 'website',
                'admin_notes' => 'Provided complete guidance on home buying process. Customer satisfied.',
                'created_at' => Carbon::now()->subDays(7),
            ],
            [
                'name' => 'David Johnson',
                'email' => '<EMAIL>',
                'phone' => '******-567-8900',
                'subject' => 'NRI Property Investment',
                'message' => 'I am an NRI based in USA. Interested in property investment in Pune. Need information about legal procedures and documentation.',
                'enquiry_type' => 'investment',
                'preferred_contact' => 'email',
                'status' => 'new',
                'source' => 'website',
                'additional_data' => json_encode(['country' => 'USA', 'nri_status' => true]),
                'created_at' => Carbon::now()->subHours(12),
            ],
            [
                'name' => 'Anita Joshi',
                'email' => '<EMAIL>',
                'phone' => '+91 5432109876',
                'subject' => 'Property Valuation Service',
                'message' => 'I want to sell my property in Kothrud. Need property valuation and market analysis services.',
                'enquiry_type' => 'general',
                'preferred_contact' => 'phone',
                'status' => 'contacted',
                'contacted_at' => Carbon::now()->subHours(3),
                'source' => 'website',
                'created_at' => Carbon::now()->subHours(8),
            ],
            [
                'name' => 'Vikram Singh',
                'email' => '<EMAIL>',
                'phone' => '+91 4321098765',
                'subject' => 'Luxury Villa Inquiry',
                'message' => 'Looking for luxury villas in Lonavala or Khandala for weekend home. Budget 2-3 crores.',
                'enquiry_type' => 'property',
                'preferred_contact' => 'both',
                'status' => 'in_progress',
                'contacted_at' => Carbon::now()->subHours(2),
                'source' => 'website',
                'admin_notes' => 'Scheduled site visit for this weekend.',
                'created_at' => Carbon::now()->subHours(4),
            ],
        ];

        foreach ($enquiries as $enquiry) {
            ContactEnquiry::create($enquiry);
        }
    }
}
