<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Founder;

class FounderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $founders = [
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'position' => 'Founder & CEO',
                'image_path' => null, // Will use default avatar
                'bio' => 'That single question sparked the journey of <PERSON><PERSON><PERSON>, the founder of Hestia Abodes—a real estate consultancy designed not just to close deals, but to elevate how people experience property decisions.

A<PERSON><PERSON>\'s journey wasn\'t shaped in boardrooms — it was shaped on the ground: listening to uncertain buyers, guiding hesitant investors, and working closely with builders trying to do things right. Over the years, he realized what most people lacked wasn\'t access to projects—it was access to perspective.

<PERSON><PERSON><PERSON> leads a boutique advisory that values clarity over commission, process over pressure, and relationships over transactions. His calm, insight-led, and client-first approach stands out in a market that too often rushes people into decisions they\'re not ready to make.

He believes buying a home isn\'t something you should be talked into—it\'s something you should walk into, with confidence.',
                'quote' => 'Real estate is emotional. Strategic. Life-changing. You deserve more than someone selling to you. You deserve someone who\'s walking with you.',
                'principles' => [
                    [
                        'icon' => 'fas fa-cut',
                        'text' => 'Perspective that cuts through the clutter.'
                    ],
                    [
                        'icon' => 'fas fa-heart',
                        'text' => 'Advice that\'s honest, not biased.'
                    ],
                    [
                        'icon' => 'fas fa-shield-alt',
                        'text' => 'Support that doesn\'t vanish once the deal is signed.'
                    ]
                ],
                'sort_order' => 1,
                'is_active' => true
            ]
        ];

        foreach ($founders as $founder) {
            Founder::updateOrCreate(
                ['name' => $founder['name']],
                $founder
            );
        }
    }
}
