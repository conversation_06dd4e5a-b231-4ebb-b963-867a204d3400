<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ContactSection;

class ContactSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ContactSection::create([
            'title' => 'Get in Touch',
            'description' => 'Ready to start your real estate journey? Contact our expert team for personalized assistance and professional guidance.',
            'contact_info' => json_encode([
                'phone' => '+91 90678 81848',
                'email' => '<EMAIL>',
                'address' => 'Pune, Maharashtra, India',
                'working_hours' => 'Mon - Sat: 9:00 AM - 7:00 PM'
            ]),
            'background_type' => 'color',
            'background_color' => '#f8f9fa',
            'icon_color' => '#cfaa13',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        ContactSection::create([
            'title' => 'Office Location',
            'description' => 'Visit our office for face-to-face consultation and detailed property discussions.',
            'contact_info' => json_encode([
                'address' => 'Pune, Maharashtra, India',
                'landmark' => 'Near Major IT Parks',
                'parking' => 'Available',
                'accessibility' => 'Wheelchair accessible'
            ]),
            'background_type' => 'none',
            'icon_color' => '#cfaa13',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        ContactSection::create([
            'title' => 'Quick Contact',
            'description' => 'For immediate assistance, reach out to us through any of these channels.',
            'contact_info' => json_encode([
                'whatsapp' => '+91 90678 81848',
                'phone' => '+91 90678 81848',
                'email' => '<EMAIL>',
                'response_time' => 'Within 2 hours during business hours'
            ]),
            'background_type' => 'color',
            'background_color' => '#f5f5f5',
            'icon_color' => '#cfaa13',
            'sort_order' => 3,
            'is_active' => true,
        ]);
    }
}
