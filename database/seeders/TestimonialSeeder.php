<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Testimonial::create([
            'name' => '<PERSON><PERSON>rth <PERSON>',
            'designation' => 'IT Professional',
            'company' => 'Tech Solutions Pvt Ltd',
            'testimonial' => '<PERSON><PERSON><PERSON> Abodes helped me navigate the Pune real estate market like a pro. I was impressed with their transparency and personalized service. They made my home buying journey smooth and stress-free.',
            'rating' => 5,
            'sort_order' => 1,
            'is_active' => true,
            'location' => 'Pune',
        ]);

        Testimonial::create([
            'name' => '<PERSON><PERSON><PERSON>',
            'designation' => 'Investor',
            'company' => 'Investment Advisory',
            'testimonial' => 'As an investor, I appreciate the clarity and ROI-focused advice I received. Highly recommend their services! The team provided excellent market insights and helped me make informed decisions.',
            'rating' => 5,
            'sort_order' => 2,
            'is_active' => true,
            'location' => 'Mumbai',
        ]);

        Testimonial::create([
            'name' => '<PERSON>',
            'designation' => 'Software Engineer',
            'company' => 'Global Tech Inc',
            'testimonial' => 'As an NRI, I was concerned about investing in Indian real estate. Hestia Abodes made the entire process smooth and hassle-free. Their expertise in NRI investments is commendable.',
            'rating' => 5,
            'sort_order' => 3,
            'is_active' => true,
            'location' => 'USA (NRI)',
        ]);

        Testimonial::create([
            'name' => 'Priya Sharma',
            'designation' => 'Marketing Manager',
            'company' => 'Digital Marketing Agency',
            'testimonial' => 'Excellent service and professional approach. The team understood my requirements perfectly and helped me find my dream home within my budget. Truly satisfied with their service.',
            'rating' => 4,
            'sort_order' => 4,
            'is_active' => true,
            'location' => 'Gurgaon',
        ]);

        Testimonial::create([
            'name' => 'Rajesh Kumar',
            'designation' => 'Business Owner',
            'company' => 'Kumar Enterprises',
            'testimonial' => 'Professional team with deep market knowledge. They provided valuable insights that helped me make the right investment decision. The entire process was transparent and efficient.',
            'rating' => 5,
            'sort_order' => 5,
            'is_active' => true,
            'location' => 'Delhi',
        ]);
    }
}
