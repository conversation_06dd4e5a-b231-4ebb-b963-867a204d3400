<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_us_sections', function (Blueprint $table) {
            $table->id();
            $table->string('section_key')->unique(); // who_we_are, our_expertise, mission_vision, etc.
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->longText('description');
            $table->string('content_type')->default('text'); // text, icon, image
            $table->string('icon_class')->nullable(); // fa fa-home, fas fa-shield-alt, etc.
            $table->string('image_path')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('additional_data')->nullable(); // For storing extra fields like lists, quotes, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_us_sections');
    }
};
