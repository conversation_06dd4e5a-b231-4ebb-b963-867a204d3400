<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->string('location');
            $table->string('city');
            $table->string('state')->default('Haryana');
            $table->string('developer');
            $table->string('project_type'); // Residential, Commercial, etc.
            $table->string('property_types'); // 1BHK, 2BHK, 3BHK, etc.
            $table->decimal('starting_price', 12, 2);
            $table->string('price_range');
            $table->string('possession_date');
            $table->string('rera_id')->nullable();
            $table->decimal('total_area', 10, 2)->nullable();
            $table->integer('total_units')->nullable();
            $table->enum('status', ['upcoming', 'ongoing', 'ready_to_move', 'completed'])->default('upcoming');
            $table->boolean('featured')->default(false);
            $table->json('images')->nullable();
            $table->json('floor_plans')->nullable();
            $table->json('amenities')->nullable();
            $table->json('specifications')->nullable();
            $table->text('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
