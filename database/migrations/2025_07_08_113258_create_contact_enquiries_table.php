<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_enquiries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone');
            $table->string('subject')->nullable();
            $table->text('message');
            $table->string('enquiry_type')->default('general'); // general, property, investment
            $table->string('preferred_contact')->default('email'); // email, phone, both
            $table->string('status')->default('new'); // new, contacted, in_progress, resolved, closed
            $table->text('admin_notes')->nullable();
            $table->timestamp('contacted_at')->nullable();
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->string('source')->default('website'); // website, phone, referral, etc.
            $table->json('additional_data')->nullable(); // for any extra form fields
            $table->timestamps();

            $table->foreign('assigned_to')->references('id')->on('admins')->onDelete('set null');
            $table->index(['status', 'created_at']);
            $table->index(['enquiry_type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_enquiries');
    }
};
