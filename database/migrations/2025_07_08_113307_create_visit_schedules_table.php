<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visit_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone');
            $table->string('property_type')->nullable(); // apartment, villa, commercial, etc.
            $table->string('location_preference')->nullable();
            $table->string('budget_range')->nullable();
            $table->date('preferred_date');
            $table->time('preferred_time');
            $table->date('alternate_date')->nullable();
            $table->time('alternate_time')->nullable();
            $table->text('special_requirements')->nullable();
            $table->string('status')->default('pending'); // pending, confirmed, completed, cancelled, rescheduled
            $table->timestamp('confirmed_at')->nullable();
            $table->date('scheduled_date')->nullable();
            $table->time('scheduled_time')->nullable();
            $table->unsignedBigInteger('assigned_agent')->nullable();
            $table->text('agent_notes')->nullable();
            $table->string('visit_type')->default('property_viewing'); // property_viewing, consultation, site_visit
            $table->json('additional_info')->nullable();
            $table->timestamps();

            $table->foreign('assigned_agent')->references('id')->on('admins')->onDelete('set null');
            $table->index(['status', 'preferred_date']);
            $table->index(['scheduled_date', 'scheduled_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visit_schedules');
    }
};
