<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('about_us_sections', function (Blueprint $table) {
            $table->string('image_position')->default('left')->after('image_path'); // left, right, top, bottom
            $table->integer('columns_count')->default(1)->after('image_position'); // Number of columns for subcontent
            $table->json('subcontent')->nullable()->after('columns_count'); // Array of subcontent items
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('about_us_sections', function (Blueprint $table) {
            $table->dropColumn(['image_position', 'columns_count', 'subcontent']);
        });
    }
};
