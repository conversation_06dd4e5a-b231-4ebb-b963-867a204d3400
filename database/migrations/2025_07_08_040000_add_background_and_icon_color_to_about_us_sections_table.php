<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('about_us_sections', function (Blueprint $table) {
            $table->string('background_type')->default('none')->after('subcontent'); // none, color, image
            $table->string('background_color')->nullable()->after('background_type');
            $table->string('background_image')->nullable()->after('background_color');
            $table->string('icon_color')->default('#ffffff')->after('background_image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('about_us_sections', function (Blueprint $table) {
            $table->dropColumn(['background_type', 'background_color', 'background_image', 'icon_color']);
        });
    }
};
