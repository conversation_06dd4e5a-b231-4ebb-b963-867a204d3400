<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            // Add missing columns that the model expects
            if (!Schema::hasColumn('home_contents', 'images')) {
                $table->json('images')->nullable()->after('content');
            }
            if (!Schema::hasColumn('home_contents', 'background_type')) {
                $table->string('background_type')->default('none')->after('sort_order');
            }
            if (!Schema::hasColumn('home_contents', 'background_color')) {
                $table->string('background_color')->nullable()->after('background_type');
            }
            if (!Schema::hasColumn('home_contents', 'background_image')) {
                $table->string('background_image')->nullable()->after('background_color');
            }
            if (!Schema::hasColumn('home_contents', 'icon_color')) {
                $table->string('icon_color')->default('#cfaa13')->after('background_image');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            $table->dropColumn(['images', 'background_type', 'background_color', 'background_image', 'icon_color']);
        });
    }
};
