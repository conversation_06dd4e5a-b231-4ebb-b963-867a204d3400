<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            // Add missing columns that the model expects
            if (!Schema::hasColumn('home_contents', 'item_type')) {
                $table->string('item_type')->nullable()->after('section_name');
            }
            if (!Schema::hasColumn('home_contents', 'image')) {
                $table->string('image')->nullable()->after('content');
            }
            if (!Schema::hasColumn('home_contents', 'icon')) {
                $table->string('icon')->nullable()->after('icon_color');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            $table->dropColumn(['item_type', 'image', 'icon']);
        });
    }
};
