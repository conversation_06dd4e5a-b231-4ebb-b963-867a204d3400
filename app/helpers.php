<?php

if (!function_exists('setting')) {
    /**
     * Get setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        return app(\App\Services\SettingsService::class)->get($key, $default);
    }
}

if (!function_exists('settings')) {
    /**
     * Get settings service instance
     *
     * @return \App\Services\SettingsService
     */
    function settings()
    {
        return app(\App\Services\SettingsService::class);
    }
}
