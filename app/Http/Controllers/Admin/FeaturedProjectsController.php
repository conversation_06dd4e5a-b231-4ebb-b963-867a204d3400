<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FeaturedProjectsController extends Controller
{
    private $sectionName = 'featured_projects';
    private $itemType = 'projects';
    private $defaultTitle = 'Featured Projects';
    private $defaultSubtitle = 'Handpicked Premium Properties';
    private $defaultContent = '<p>Explore our carefully selected portfolio of premium residential and commercial projects.</p>';
    private $defaultButtonText = 'View All Projects';
    private $defaultButtonUrl = '/projects';
    private $sortOrder = 4;

    public function index()
    {
        $featuredProjects = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$featuredProjects) {
            $featuredProjects = HomeContent::create([
                'section_name' => $this->sectionName,
                'item_type' => $this->itemType,
                'title' => $this->defaultTitle,
                'subtitle' => $this->defaultSubtitle,
                'content' => $this->defaultContent,
                'button_text' => $this->defaultButtonText,
                'button_url' => $this->defaultButtonUrl,
                'sort_order' => $this->sortOrder,
                'is_active' => true,
            ]);
        }
        
        return view('admin.featured-projects.index', compact('featuredProjects'));
    }

    public function edit()
    {
        $featuredProjects = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$featuredProjects) {
            return redirect()->route('admin.featured-projects.index');
        }
        
        return view('admin.featured-projects.edit', compact('featuredProjects'));
    }

    public function update(Request $request)
    {
        $featuredProjects = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$featuredProjects) {
            return redirect()->route('admin.featured-projects.index')->with('error', 'Featured projects section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
        ]);

        $data = $request->except(['image', 'images']);
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('image')) {
            if ($featuredProjects->image) {
                Storage::disk('public')->delete($featuredProjects->image);
            }
            $imagePath = $request->file('image')->store('featured-projects', 'public');
            $data['image'] = $imagePath;
        }

        if ($request->hasFile('images')) {
            if ($featuredProjects->images && is_array($featuredProjects->images)) {
                foreach ($featuredProjects->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('featured-projects', 'public');
            }
            $data['images'] = $images;
        } else {
            unset($data['images']);
        }

        $featuredProjects->update($data);
        
        return redirect()->route('admin.featured-projects.index')->with('success', 'Featured projects section updated successfully');
    }

    public function toggleStatus()
    {
        $featuredProjects = HomeContent::where('section_name', $this->sectionName)->first();
        
        if ($featuredProjects) {
            $featuredProjects->update(['is_active' => !$featuredProjects->is_active]);
            $status = $featuredProjects->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.featured-projects.index')->with('success', "Featured projects section {$status} successfully");
        }
        
        return redirect()->route('admin.featured-projects.index')->with('error', 'Featured projects section not found');
    }
}
