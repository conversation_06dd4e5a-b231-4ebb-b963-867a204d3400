<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ContactCtaController extends Controller
{
    private $sectionName = 'contact_cta';
    private $itemType = 'cta';
    private $defaultTitle = 'Ready to Find Your Dream Property?';
    private $defaultSubtitle = 'Get in Touch with Our Experts';
    private $defaultContent = '<p>Contact our experienced team today for personalized real estate solutions.</p>';
    private $defaultButtonText = 'Contact Us';
    private $defaultButtonUrl = '/contact';
    private $sortOrder = 6;

    public function index()
    {
        $contactCta = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$contactCta) {
            $contactCta = HomeContent::create([
                'section_name' => $this->sectionName,
                'item_type' => $this->itemType,
                'title' => $this->defaultTitle,
                'subtitle' => $this->defaultSubtitle,
                'content' => $this->defaultContent,
                'button_text' => $this->defaultButtonText,
                'button_url' => $this->defaultButtonUrl,
                'sort_order' => $this->sortOrder,
                'is_active' => true,
            ]);
        }
        
        return view('admin.contact-cta.index', compact('contactCta'));
    }

    public function edit()
    {
        $contactCta = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$contactCta) {
            return redirect()->route('admin.contact-cta.index');
        }
        
        return view('admin.contact-cta.edit', compact('contactCta'));
    }

    public function update(Request $request)
    {
        $contactCta = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$contactCta) {
            return redirect()->route('admin.contact-cta.index')->with('error', 'Contact CTA section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except(['image', 'images', 'background_image']);
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('image')) {
            if ($contactCta->image) {
                Storage::disk('public')->delete($contactCta->image);
            }
            $imagePath = $request->file('image')->store('contact-cta', 'public');
            $data['image'] = $imagePath;
        }

        if ($request->hasFile('background_image')) {
            if ($contactCta->background_image) {
                Storage::disk('public')->delete($contactCta->background_image);
            }
            $backgroundImagePath = $request->file('background_image')->store('contact-cta/backgrounds', 'public');
            $data['background_image'] = $backgroundImagePath;
        }

        if ($request->hasFile('images')) {
            if ($contactCta->images && is_array($contactCta->images)) {
                foreach ($contactCta->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('contact-cta', 'public');
            }
            $data['images'] = $images;
        } else {
            unset($data['images']);
        }

        $contactCta->update($data);
        
        return redirect()->route('admin.contact-cta.index')->with('success', 'Contact CTA section updated successfully');
    }

    public function toggleStatus()
    {
        $contactCta = HomeContent::where('section_name', $this->sectionName)->first();
        
        if ($contactCta) {
            $contactCta->update(['is_active' => !$contactCta->is_active]);
            $status = $contactCta->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.contact-cta.index')->with('success', "Contact CTA section {$status} successfully");
        }
        
        return redirect()->route('admin.contact-cta.index')->with('error', 'Contact CTA section not found');
    }
}
