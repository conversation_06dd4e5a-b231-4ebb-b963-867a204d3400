<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TestimonialsSectionController extends Controller
{
    private $sectionName = 'testimonials_section';
    private $itemType = 'testimonials';
    private $defaultTitle = 'What Our Clients Say';
    private $defaultSubtitle = 'Trusted by Hundreds of Happy Clients';
    private $defaultContent = '<p>Read what our satisfied clients have to say about their experience with Hestia Abodes.</p>';
    private $sortOrder = 5;

    public function index()
    {
        $testimonialsSection = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$testimonialsSection) {
            $testimonialsSection = HomeContent::create([
                'section_name' => $this->sectionName,
                'item_type' => $this->itemType,
                'title' => $this->defaultTitle,
                'subtitle' => $this->defaultSubtitle,
                'content' => $this->defaultContent,
                'sort_order' => $this->sortOrder,
                'is_active' => true,
            ]);
        }
        
        return view('admin.testimonials-section.index', compact('testimonialsSection'));
    }

    public function edit()
    {
        $testimonialsSection = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$testimonialsSection) {
            return redirect()->route('admin.testimonials-section.index');
        }
        
        return view('admin.testimonials-section.edit', compact('testimonialsSection'));
    }

    public function update(Request $request)
    {
        $testimonialsSection = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$testimonialsSection) {
            return redirect()->route('admin.testimonials-section.index')->with('error', 'Testimonials section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
        ]);

        $data = $request->except(['image', 'images']);
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('image')) {
            if ($testimonialsSection->image) {
                Storage::disk('public')->delete($testimonialsSection->image);
            }
            $imagePath = $request->file('image')->store('testimonials-section', 'public');
            $data['image'] = $imagePath;
        }

        if ($request->hasFile('images')) {
            if ($testimonialsSection->images && is_array($testimonialsSection->images)) {
                foreach ($testimonialsSection->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('testimonials-section', 'public');
            }
            $data['images'] = $images;
        } else {
            unset($data['images']);
        }

        $testimonialsSection->update($data);
        
        return redirect()->route('admin.testimonials-section.index')->with('success', 'Testimonials section updated successfully');
    }

    public function toggleStatus()
    {
        $testimonialsSection = HomeContent::where('section_name', $this->sectionName)->first();
        
        if ($testimonialsSection) {
            $testimonialsSection->update(['is_active' => !$testimonialsSection->is_active]);
            $status = $testimonialsSection->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.testimonials-section.index')->with('success', "Testimonials section {$status} successfully");
        }
        
        return redirect()->route('admin.testimonials-section.index')->with('error', 'Testimonials section not found');
    }
}
