<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServicesPreviewController extends Controller
{
    private $sectionName = 'services_preview';
    private $itemType = 'services';
    private $defaultTitle = 'Our Services';
    private $defaultSubtitle = 'Comprehensive Real Estate Solutions';
    private $defaultContent = '<p>From property buying and selling to investment advisory and legal support, we offer a complete range of real estate services.</p>';
    private $defaultButtonText = 'View All Services';
    private $defaultButtonUrl = '/services';
    private $sortOrder = 3;

    /**
     * Display the services preview management page.
     */
    public function index()
    {
        $servicesPreview = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$servicesPreview) {
            $servicesPreview = HomeContent::create([
                'section_name' => $this->sectionName,
                'item_type' => $this->itemType,
                'title' => $this->defaultTitle,
                'subtitle' => $this->defaultSubtitle,
                'content' => $this->defaultContent,
                'button_text' => $this->defaultButtonText,
                'button_url' => $this->defaultButtonUrl,
                'sort_order' => $this->sortOrder,
                'is_active' => true,
            ]);
        }
        
        return view('admin.services-preview.index', compact('servicesPreview'));
    }

    /**
     * Show the form for editing the services preview section.
     */
    public function edit()
    {
        $servicesPreview = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$servicesPreview) {
            return redirect()->route('admin.services-preview.index');
        }
        
        return view('admin.services-preview.edit', compact('servicesPreview'));
    }

    /**
     * Update the services preview section.
     */
    public function update(Request $request)
    {
        $servicesPreview = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$servicesPreview) {
            return redirect()->route('admin.services-preview.index')->with('error', 'Services preview section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'icon' => 'nullable|string|max:100',
            'icon_color' => 'nullable|string|max:7',
            'background_color' => 'nullable|string|max:7',
        ]);

        $data = $request->except(['image', 'images']);
        $data['is_active'] = $request->has('is_active');

        // Handle main image upload
        if ($request->hasFile('image')) {
            if ($servicesPreview->image) {
                Storage::disk('public')->delete($servicesPreview->image);
            }
            $imagePath = $request->file('image')->store('services-preview', 'public');
            $data['image'] = $imagePath;
        }

        // Handle multiple images upload
        if ($request->hasFile('images')) {
            if ($servicesPreview->images && is_array($servicesPreview->images)) {
                foreach ($servicesPreview->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('services-preview', 'public');
            }
            $data['images'] = $images;
        } else {
            unset($data['images']);
        }

        $servicesPreview->update($data);
        
        return redirect()->route('admin.services-preview.index')->with('success', 'Services preview section updated successfully');
    }

    /**
     * Toggle services preview section status.
     */
    public function toggleStatus()
    {
        $servicesPreview = HomeContent::where('section_name', $this->sectionName)->first();
        
        if ($servicesPreview) {
            $servicesPreview->update(['is_active' => !$servicesPreview->is_active]);
            $status = $servicesPreview->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.services-preview.index')->with('success', "Services preview section {$status} successfully");
        }
        
        return redirect()->route('admin.services-preview.index')->with('error', 'Services preview section not found');
    }
}
