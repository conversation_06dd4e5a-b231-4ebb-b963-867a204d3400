<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Slider;
use Illuminate\Support\Facades\Storage;

class HomeSliderController extends Controller
{
    public function index()
    {
        $sliders = Slider::orderBy('sort_order', 'asc')
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);
        return view('admin.home-slider.index', compact('sliders'));
    }

    public function create()
    {
        return view('admin.home-slider.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string|max:1000',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|url|max:500',
            'button_text_2' => 'nullable|string|max:100',
            'button_link_2' => 'nullable|url|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500'
        ]);

        $data = $request->except('image');

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('sliders', 'public');
            $data['image_path'] = $imagePath;
        }

        Slider::create($data);

        return redirect()->route('admin.sliders.index')
                        ->with('success', 'Home slider created successfully!');
    }

    public function show(Slider $slider)
    {
        return view('admin.home-slider.show', compact('slider'));
    }

    public function edit(Slider $slider)
    {
        return view('admin.home-slider.edit', compact('slider'));
    }

    public function update(Request $request, Slider $slider)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|url|max:500',
            'button_text_2' => 'nullable|string|max:100',
            'button_link_2' => 'nullable|url|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500'
        ]);

        $data = $request->except('image');

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($slider->image_path && Storage::disk('public')->exists($slider->image_path)) {
                Storage::disk('public')->delete($slider->image_path);
            }

            $imagePath = $request->file('image')->store('sliders', 'public');
            $data['image_path'] = $imagePath;
        }

        $slider->update($data);

        return redirect()->route('admin.sliders.index')
                        ->with('success', 'Home slider updated successfully!');
    }

    public function destroy(Slider $slider)
    {
        // Delete associated image
        if ($slider->image_path && Storage::disk('public')->exists($slider->image_path)) {
            Storage::disk('public')->delete($slider->image_path);
        }

        $slider->delete();

        return redirect()->route('admin.sliders.index')
                        ->with('success', 'Home slider deleted successfully!');
    }
}
