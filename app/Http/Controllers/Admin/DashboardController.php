<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_projects' => Project::count(),
            'active_projects' => Project::where('is_active', true)->count(),
            'featured_projects' => Project::where('featured', true)->count(),
            'upcoming_projects' => Project::where('status', 'upcoming')->count(),
            'ongoing_projects' => Project::where('status', 'ongoing')->count(),
            'ready_projects' => Project::where('status', 'ready_to_move')->count(),
            'recent_projects' => Project::latest()->take(5)->get(),
        ];

        return view('admin.dashboard', compact('stats'));
    }
}
