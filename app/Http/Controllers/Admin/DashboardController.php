<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ContactEnquiry;
use App\Models\VisitSchedule;
use App\Models\Testimonial;
use App\Models\Blog;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Get comprehensive dashboard statistics
        $stats = [
            // Project Statistics
            'total_projects' => Project::count(),
            'active_projects' => Project::where('is_active', true)->count(),
            'featured_projects' => Project::where('featured', true)->count(),
            'upcoming_projects' => Project::where('status', 'upcoming')->count(),
            'ongoing_projects' => Project::where('status', 'ongoing')->count(),
            'ready_projects' => Project::where('status', 'ready_to_move')->count(),

            // Contact & Lead Statistics
            'total_enquiries' => ContactEnquiry::count(),
            'new_enquiries' => ContactEnquiry::where('status', 'new')->count(),
            'enquiries_today' => ContactEnquiry::whereDate('created_at', Carbon::today())->count(),
            'enquiries_this_week' => ContactEnquiry::whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count(),
            'enquiries_this_month' => ContactEnquiry::whereMonth('created_at', Carbon::now()->month)->count(),

            // Visit Schedule Statistics
            'total_visits' => VisitSchedule::count(),
            'pending_visits' => VisitSchedule::where('status', 'pending')->count(),
            'confirmed_visits' => VisitSchedule::where('status', 'confirmed')->count(),
            'visits_today' => VisitSchedule::where('scheduled_date', Carbon::today())->count(),
            'visits_this_week' => VisitSchedule::whereBetween('scheduled_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count(),

            // Content Statistics
            'total_testimonials' => Testimonial::count(),
            'active_testimonials' => Testimonial::where('is_active', true)->count(),
            'total_blogs' => Blog::count(),
            'published_blogs' => Blog::where('is_published', true)->count(),
            'total_services' => Service::count(),
            'active_services' => Service::where('is_active', true)->count(),
        ];

        // Recent Activity Data
        $recentData = [
            'recent_projects' => Project::latest()->take(5)->get(),
            'recent_enquiries' => ContactEnquiry::with('assignedAdmin')->latest()->take(5)->get(),
            'upcoming_visits' => VisitSchedule::with('assignedAgent')
                ->where('status', 'confirmed')
                ->where('scheduled_date', '>=', Carbon::today())
                ->orderBy('scheduled_date')
                ->orderBy('scheduled_time')
                ->take(5)
                ->get(),
            'recent_testimonials' => Testimonial::latest()->take(3)->get(),
        ];

        // Chart Data for Analytics
        $chartData = [
            'enquiries_last_7_days' => $this->getEnquiriesLast7Days(),
            'visits_last_7_days' => $this->getVisitsLast7Days(),
            'enquiry_types' => $this->getEnquiryTypeDistribution(),
            'visit_status_distribution' => $this->getVisitStatusDistribution(),
        ];

        return view('admin.dashboard', compact('stats', 'recentData', 'chartData'));
    }

    private function getEnquiriesLast7Days()
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $data[] = [
                'date' => $date->format('M d'),
                'count' => ContactEnquiry::whereDate('created_at', $date)->count()
            ];
        }
        return $data;
    }

    private function getVisitsLast7Days()
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $data[] = [
                'date' => $date->format('M d'),
                'count' => VisitSchedule::whereDate('created_at', $date)->count()
            ];
        }
        return $data;
    }

    private function getEnquiryTypeDistribution()
    {
        return ContactEnquiry::selectRaw('enquiry_type, COUNT(*) as count')
            ->groupBy('enquiry_type')
            ->get()
            ->mapWithKeys(function ($item) {
                return [ucfirst($item->enquiry_type) => $item->count];
            });
    }

    private function getVisitStatusDistribution()
    {
        return VisitSchedule::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->mapWithKeys(function ($item) {
                return [ucfirst($item->status) => $item->count];
            });
    }
}
