<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects.
     */
    public function index(Request $request)
    {
        $query = Project::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('developer', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by city
        if ($request->filled('city')) {
            $query->where('city', $request->get('city'));
        }

        // Filter by project type
        if ($request->filled('project_type')) {
            $query->where('project_type', $request->get('project_type'));
        }

        $projects = $query->latest()->paginate(15);
        
        // Get unique cities and project types for filters
        $cities = Project::distinct()->pluck('city')->filter();
        $projectTypes = Project::distinct()->pluck('project_type')->filter();

        return view('admin.projects.index', compact('projects', 'cities', 'projectTypes'));
    }

    /**
     * Show the form for creating a new project.
     */
    public function create()
    {
        return view('admin.projects.create');
    }

    /**
     * Store a newly created project.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'location' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'developer' => 'required|string|max:255',
            'project_type' => 'required|string|max:255',
            'property_types' => 'required|string|max:255',
            'starting_price' => 'required|numeric|min:0',
            'price_range' => 'required|string|max:255',
            'possession_date' => 'required|string|max:255',
            'rera_id' => 'nullable|string|max:255',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'nullable|integer|min:0',
            'status' => 'required|in:upcoming,ongoing,ready_to_move,completed',
            'featured' => 'boolean',
            'feature_slider' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'brochure_file' => 'nullable|file|mimes:pdf|max:10240', // 10MB max
            'icon_color' => 'nullable|string|max:7',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Project::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set default sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = Project::max('sort_order') + 1;
        }

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            $backgroundImagePath = $request->file('background_image')->store('projects/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        // Handle brochure file upload
        if ($request->hasFile('brochure_file')) {
            $brochurePath = $request->file('brochure_file')->store('projects/brochures', 'public');
            $validated['brochure_file'] = $brochurePath;
        }

        Project::create($validated);

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project created successfully.');
    }

    /**
     * Display the specified project.
     */
    public function show(Project $project)
    {
        return view('admin.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified project.
     */
    public function edit(Project $project)
    {
        return view('admin.projects.edit', compact('project'));
    }

    /**
     * Update the specified project.
     */
    public function update(Request $request, Project $project)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'location' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'developer' => 'required|string|max:255',
            'project_type' => 'required|string|max:255',
            'property_types' => 'required|string|max:255',
            'starting_price' => 'required|numeric|min:0',
            'price_range' => 'required|string|max:255',
            'possession_date' => 'required|string|max:255',
            'rera_id' => 'nullable|string|max:255',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'nullable|integer|min:0',
            'status' => 'required|in:upcoming,ongoing,ready_to_move,completed',
            'featured' => 'boolean',
            'feature_slider' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'brochure_file' => 'nullable|file|mimes:pdf|max:10240', // 10MB max
            'icon_color' => 'nullable|string|max:7',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $project->name) {
            $validated['slug'] = Str::slug($validated['name']);

            // Ensure slug is unique
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (Project::where('slug', $validated['slug'])->where('id', '!=', $project->id)->exists()) {
                $validated['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            // Delete old background image if exists
            if ($project->background_image) {
                Storage::disk('public')->delete($project->background_image);
            }
            $backgroundImagePath = $request->file('background_image')->store('projects/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        // Handle brochure file upload
        if ($request->hasFile('brochure_file')) {
            // Delete old brochure file if exists
            if ($project->brochure_file) {
                Storage::disk('public')->delete($project->brochure_file);
            }
            $brochurePath = $request->file('brochure_file')->store('projects/brochures', 'public');
            $validated['brochure_file'] = $brochurePath;
        }

        $project->update($validated);

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project updated successfully.');
    }

    /**
     * Remove the specified project.
     */
    public function destroy(Project $project)
    {
        $project->delete();

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project deleted successfully.');
    }
}
