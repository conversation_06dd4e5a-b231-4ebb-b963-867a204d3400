<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\Admin;
use App\Models\Slider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
        // Get dashboard statistics
        $stats = [
            'total_projects' => Project::count(),
            'active_projects' => Project::where('is_active', true)->count(),
            'featured_projects' => Project::where('featured', true)->count(),
            'upcoming_projects' => Project::where('status', 'upcoming')->count(),
            'ongoing_projects' => Project::where('status', 'ongoing')->count(),
            'ready_projects' => Project::where('status', 'ready_to_move')->count(),
            'total_admins' => Admin::count(),
            'total_sliders' => Slider::count(),
            'active_sliders' => Slider::where('is_active', true)->count(),
            'recent_projects' => Project::latest()->take(5)->get(),
            'recent_sliders' => Slider::latest()->take(3)->get(),
        ];

        return view('admin.dashboard', compact('stats'));
    }
}
