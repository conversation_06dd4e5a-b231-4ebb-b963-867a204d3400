<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactEnquiry;
use App\Models\VisitSchedule;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display contact enquiries and visit schedules.
     */
    public function index(Request $request)
    {
        $tab = $request->get('tab', 'enquiries');

        // Get contact enquiries with search and filter
        $enquiriesQuery = ContactEnquiry::with('assignedAdmin')
            ->orderBy('created_at', 'desc');

        if ($request->filled('enquiry_search')) {
            $search = $request->enquiry_search;
            $enquiriesQuery->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        if ($request->filled('enquiry_status')) {
            $enquiriesQuery->where('status', $request->enquiry_status);
        }

        if ($request->filled('enquiry_type')) {
            $enquiriesQuery->where('enquiry_type', $request->enquiry_type);
        }

        $enquiries = $enquiriesQuery->paginate(10, ['*'], 'enquiries_page');

        // Get visit schedules with search and filter
        $visitsQuery = VisitSchedule::with('assignedAgent')
            ->orderBy('preferred_date', 'desc');

        if ($request->filled('visit_search')) {
            $search = $request->visit_search;
            $visitsQuery->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('property_type', 'like', "%{$search}%")
                  ->orWhere('location_preference', 'like', "%{$search}%");
            });
        }

        if ($request->filled('visit_status')) {
            $visitsQuery->where('status', $request->visit_status);
        }

        if ($request->filled('visit_type')) {
            $visitsQuery->where('visit_type', $request->visit_type);
        }

        $visits = $visitsQuery->paginate(10, ['*'], 'visits_page');

        // Get statistics
        $stats = [
            'total_enquiries' => ContactEnquiry::count(),
            'new_enquiries' => ContactEnquiry::where('status', 'new')->count(),
            'pending_visits' => VisitSchedule::where('status', 'pending')->count(),
            'confirmed_visits' => VisitSchedule::where('status', 'confirmed')->count(),
        ];

        return view('admin.contact.index', compact('enquiries', 'visits', 'tab', 'stats'));
    }

    /**
     * Show the specified enquiry.
     */
    public function showEnquiry(ContactEnquiry $enquiry)
    {
        return view('admin.contact.show-enquiry', compact('enquiry'));
    }

    /**
     * Update enquiry status.
     */
    public function updateEnquiryStatus(Request $request, ContactEnquiry $enquiry)
    {
        $validated = $request->validate([
            'status' => 'required|in:new,contacted,in_progress,resolved,closed',
            'admin_notes' => 'nullable|string',
            'assigned_to' => 'nullable|exists:admins,id'
        ]);

        if ($validated['status'] === 'contacted' && !$enquiry->contacted_at) {
            $validated['contacted_at'] = now();
        }

        $enquiry->update($validated);

        return redirect()->route('admin.contact.index', ['tab' => 'enquiries'])
            ->with('success', 'Enquiry status updated successfully.');
    }

    /**
     * Show the specified visit schedule.
     */
    public function showVisit(VisitSchedule $visit)
    {
        return view('admin.contact.show-visit', compact('visit'));
    }

    /**
     * Update visit status.
     */
    public function updateVisitStatus(Request $request, VisitSchedule $visit)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,completed,cancelled,rescheduled',
            'scheduled_date' => 'nullable|date',
            'scheduled_time' => 'nullable|date_format:H:i',
            'agent_notes' => 'nullable|string',
            'assigned_agent' => 'nullable|exists:admins,id'
        ]);

        if ($validated['status'] === 'confirmed' && !$visit->confirmed_at) {
            $validated['confirmed_at'] = now();
        }

        $visit->update($validated);

        return redirect()->route('admin.contact.index', ['tab' => 'visits'])
            ->with('success', 'Visit status updated successfully.');
    }

    /**
     * Delete an enquiry.
     */
    public function deleteEnquiry(ContactEnquiry $enquiry)
    {
        $enquiry->delete();

        return redirect()->route('admin.contact.index', ['tab' => 'enquiries'])
            ->with('success', 'Enquiry deleted successfully.');
    }

    /**
     * Delete a visit schedule.
     */
    public function deleteVisit(VisitSchedule $visit)
    {
        $visit->delete();

        return redirect()->route('admin.contact.index', ['tab' => 'visits'])
            ->with('success', 'Visit schedule deleted successfully.');
    }
}
