<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ContactController extends Controller
{
    /**
     * Display a listing of contact sections.
     */
    public function index()
    {
        $contactSections = ContactSection::ordered()->get();
        return view('admin.contact.index', compact('contactSections'));
    }

    /**
     * Show the form for creating a new contact section.
     */
    public function create()
    {
        return view('admin.contact.create');
    }

    /**
     * Store a newly created contact section in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'contact_info' => 'nullable|string',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon_color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            $backgroundImagePath = $request->file('background_image')->store('contact/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        // Set default sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = ContactSection::max('sort_order') + 1;
        }

        ContactSection::create($validated);

        return redirect()->route('admin.contact.index')
            ->with('success', 'Contact section created successfully.');
    }

    /**
     * Display the specified contact section.
     */
    public function show(ContactSection $contact)
    {
        return view('admin.contact.show', compact('contact'));
    }

    /**
     * Show the form for editing the specified contact section.
     */
    public function edit(ContactSection $contact)
    {
        return view('admin.contact.edit', compact('contact'));
    }

    /**
     * Update the specified contact section in storage.
     */
    public function update(Request $request, ContactSection $contact)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'contact_info' => 'nullable|string',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon_color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            // Delete old background image if exists
            if ($contact->background_image) {
                Storage::disk('public')->delete($contact->background_image);
            }
            $backgroundImagePath = $request->file('background_image')->store('contact/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        $contact->update($validated);

        return redirect()->route('admin.contact.index')
            ->with('success', 'Contact section updated successfully.');
    }

    /**
     * Remove the specified contact section from storage.
     */
    public function destroy(ContactSection $contact)
    {
        // Delete background image if exists
        if ($contact->background_image) {
            Storage::disk('public')->delete($contact->background_image);
        }

        $contact->delete();

        return redirect()->route('admin.contact.index')
            ->with('success', 'Contact section deleted successfully.');
    }


}
