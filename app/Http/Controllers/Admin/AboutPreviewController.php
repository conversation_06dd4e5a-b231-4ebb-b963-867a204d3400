<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AboutPreviewController extends Controller
{
    /**
     * Display the about preview management page.
     */
    public function index()
    {
        $aboutPreview = HomeContent::where('section_name', 'about_preview')->first();
        
        if (!$aboutPreview) {
            // Create default about preview section if it doesn't exist
            $aboutPreview = HomeContent::create([
                'section_name' => 'about_preview',
                'item_type' => 'about',
                'title' => 'Why Choose Hestia Abodes?',
                'subtitle' => 'Excellence in Real Estate Services',
                'content' => '<p>With years of experience in the real estate industry, we bring expertise, transparency, and personalized service to every transaction.</p>',
                'button_text' => 'Learn More',
                'button_url' => '/about-us',
                'sort_order' => 2,
                'is_active' => true,
            ]);
        }
        
        return view('admin.about-preview.index', compact('aboutPreview'));
    }

    /**
     * Show the form for editing the about preview section.
     */
    public function edit()
    {
        $aboutPreview = HomeContent::where('section_name', 'about_preview')->first();
        
        if (!$aboutPreview) {
            return redirect()->route('admin.about-preview.index');
        }
        
        return view('admin.about-preview.edit', compact('aboutPreview'));
    }

    /**
     * Update the about preview section.
     */
    public function update(Request $request)
    {
        $aboutPreview = HomeContent::where('section_name', 'about_preview')->first();
        
        if (!$aboutPreview) {
            return redirect()->route('admin.about-preview.index')->with('error', 'About preview section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'icon' => 'nullable|string|max:100',
            'icon_color' => 'nullable|string|max:7',
        ]);

        $data = $request->except(['image', 'images']);
        $data['is_active'] = $request->has('is_active');

        // Handle main image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($aboutPreview->image) {
                Storage::disk('public')->delete($aboutPreview->image);
            }

            $imagePath = $request->file('image')->store('about-preview', 'public');
            $data['image'] = $imagePath;
        }

        // Handle multiple images upload
        if ($request->hasFile('images')) {
            // Delete old images from storage
            if ($aboutPreview->images && is_array($aboutPreview->images)) {
                foreach ($aboutPreview->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            // Store new images
            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('about-preview', 'public');
            }
            $data['images'] = $images;
        } else {
            // Keep existing images if no new images uploaded
            unset($data['images']);
        }

        $aboutPreview->update($data);
        
        return redirect()->route('admin.about-preview.index')->with('success', 'About preview section updated successfully');
    }

    /**
     * Toggle about preview section status.
     */
    public function toggleStatus()
    {
        $aboutPreview = HomeContent::where('section_name', 'about_preview')->first();
        
        if ($aboutPreview) {
            $aboutPreview->update(['is_active' => !$aboutPreview->is_active]);
            $status = $aboutPreview->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.about-preview.index')->with('success', "About preview section {$status} successfully");
        }
        
        return redirect()->route('admin.about-preview.index')->with('error', 'About preview section not found');
    }
}
