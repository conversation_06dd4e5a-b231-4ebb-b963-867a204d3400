<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServiceController extends Controller
{
    /**
     * Display a listing of services.
     */
    public function index()
    {
        $services = Service::all();
        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new service.
     */
    public function create()
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created service.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon_color' => 'nullable|string|max:7',
        ]);

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            $backgroundImagePath = $request->file('background_image')->store('services/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        Service::create($validated);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified service.
     */
    public function show(Service $service)
    {
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service.
     */
    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified service.
     */
    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'background_type' => 'required|in:none,color,image',
            'background_color' => 'nullable|string|max:7',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon_color' => 'nullable|string|max:7',
        ]);

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            // Delete old background image if exists
            if ($service->background_image) {
                Storage::disk('public')->delete($service->background_image);
            }
            $backgroundImagePath = $request->file('background_image')->store('services/backgrounds', 'public');
            $validated['background_image'] = $backgroundImagePath;
        }

        $service->update($validated);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified service.
     */
    public function destroy(Service $service)
    {
        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'Service deleted successfully.');
    }
}
