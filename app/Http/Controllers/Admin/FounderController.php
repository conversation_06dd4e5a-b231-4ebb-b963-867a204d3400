<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Founder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FounderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $founders = Founder::ordered()->paginate(10);
        return view('admin.founders.index', compact('founders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.founders.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'bio' => 'required|string',
            'quote' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->except(['image']);

        // Handle principles processing
        if ($request->has('principles')) {
            $principles = [];
            foreach ($request->principles as $principle) {
                if (!empty($principle['text'])) {
                    $principles[] = [
                        'icon' => $principle['icon'] ?? 'fas fa-check',
                        'text' => $principle['text']
                    ];
                }
            }
            $data['principles'] = $principles;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('founders', 'public');
            $data['image_path'] = $imagePath;
        }

        Founder::create($data);

        return redirect()->route('admin.founders.index')
            ->with('success', 'Founder created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Founder $founder)
    {
        return view('admin.founders.show', compact('founder'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Founder $founder)
    {
        return view('admin.founders.edit', compact('founder'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Founder $founder)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'bio' => 'required|string',
            'quote' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->except(['image']);

        // Handle principles processing
        if ($request->has('principles')) {
            $principles = [];
            foreach ($request->principles as $principle) {
                if (!empty($principle['text'])) {
                    $principles[] = [
                        'icon' => $principle['icon'] ?? 'fas fa-check',
                        'text' => $principle['text']
                    ];
                }
            }
            $data['principles'] = $principles;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($founder->image_path) {
                Storage::disk('public')->delete($founder->image_path);
            }

            $imagePath = $request->file('image')->store('founders', 'public');
            $data['image_path'] = $imagePath;
        }

        $founder->update($data);

        return redirect()->route('admin.founders.index')
            ->with('success', 'Founder updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Founder $founder)
    {
        // Delete image if exists
        if ($founder->image_path) {
            Storage::disk('public')->delete($founder->image_path);
        }

        $founder->delete();

        return redirect()->route('admin.founders.index')
            ->with('success', 'Founder deleted successfully.');
    }
}
