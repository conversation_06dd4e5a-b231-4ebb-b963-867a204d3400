<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\HomeContent;

class HomeContentController extends Controller
{
    public function index()
    {
        $contents = HomeContent::orderBy('sort_order')->get();
        return view('admin.home-content.index', compact('contents'));
    }

    public function create()
    {
        return view('admin.home-content.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'section_name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url',
            'sort_order' => 'nullable|integer'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('home-content', 'public');
            }
            $data['images'] = $images;
        }

        HomeContent::create($data);
        
        return redirect()->route('admin.home-content.index')->with('success', 'Content created successfully');
    }

    public function edit(HomeContent $homeContent)
    {
        return view('admin.home-content.edit', compact('homeContent'));
    }

    public function update(Request $request, HomeContent $homeContent)
    {
        $request->validate([
            'section_name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url',
            'sort_order' => 'nullable|integer'
        ]);

        $data = $request->all();
        
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('home-content', 'public');
            }
            $data['images'] = $images;
        }

        $homeContent->update($data);
        
        return redirect()->route('admin.home-content.index')->with('success', 'Content updated successfully');
    }

    public function destroy(HomeContent $homeContent)
    {
        $homeContent->delete();
        return redirect()->route('admin.home-content.index')->with('success', 'Content deleted successfully');
    }
}
