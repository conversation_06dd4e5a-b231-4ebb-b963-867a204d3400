<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class WhyChooseUsController extends Controller
{
    private $sectionName = 'why_choose_us';
    private $itemType = 'features';
    private $defaultTitle = 'Why Choose Hestia Abodes?';
    private $defaultSubtitle = 'Where every decision is backed by trust';
    private $defaultContent = '<p>We bring expertise, transparency, and personalized service to every transaction.</p>';
    private $sortOrder = 3;

    public function index()
    {
        $whyChooseUs = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$whyChooseUs) {
            $whyChooseUs = HomeContent::create([
                'section_name' => $this->sectionName,
                'item_type' => $this->itemType,
                'title' => $this->defaultTitle,
                'subtitle' => $this->defaultSubtitle,
                'content' => $this->defaultContent,
                'sort_order' => $this->sortOrder,
                'is_active' => true,
                'additional_data' => [
                    'features' => [
                        [
                            'icon' => 'fas fa-search',
                            'icon_color' => '#D4AF37',
                            'title' => 'Selective, Not Generic',
                            'description' => 'We only recommend what fits you. Our curated approach ensures you see properties that truly match your needs and preferences.'
                        ],
                        [
                            'icon' => 'fas fa-users',
                            'icon_color' => '#D4AF37',
                            'title' => 'We Represent You, Not the Developer',
                            'description' => 'Your goals come first. As independent consultants, we provide unbiased advice focused on your best interests.'
                        ],
                        [
                            'icon' => 'fas fa-chart-line',
                            'icon_color' => '#D4AF37',
                            'title' => 'Market Insight Over Marketing',
                            'description' => 'We share context, not just content. Get real market analysis and honest feedback, not sales pitches.'
                        ],
                        [
                            'icon' => 'fas fa-list-alt',
                            'icon_color' => '#D4AF37',
                            'title' => 'Process-Driven',
                            'description' => 'You always know what\'s happening next. Our structured approach keeps you informed at every step.'
                        ],
                        [
                            'icon' => 'fas fa-shield-alt',
                            'icon_color' => '#D4AF37',
                            'title' => 'Compliance-First',
                            'description' => 'We protect your investment with legal clarity. All transactions are handled with complete regulatory compliance.'
                        ],
                        [
                            'icon' => 'fas fa-handshake',
                            'icon_color' => '#D4AF37',
                            'title' => 'Here for the Long Haul',
                            'description' => 'Even after deal closure, we remain your advisors. Our relationship continues beyond the transaction.'
                        ]
                    ]
                ]
            ]);
        }
        
        return view('admin.why-choose-us.index', compact('whyChooseUs'));
    }

    public function edit()
    {
        $whyChooseUs = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$whyChooseUs) {
            return redirect()->route('admin.why-choose-us.index');
        }
        
        return view('admin.why-choose-us.edit', compact('whyChooseUs'));
    }

    public function update(Request $request)
    {
        $whyChooseUs = HomeContent::where('section_name', $this->sectionName)->first();
        
        if (!$whyChooseUs) {
            return redirect()->route('admin.why-choose-us.index')->with('error', 'Why Choose Us section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
            'features' => 'required|array|min:1',
            'features.*.icon' => 'required|string|max:100',
            'features.*.icon_color' => 'required|string|max:7',
            'features.*.title' => 'required|string|max:255',
            'features.*.description' => 'required|string|max:1000',
        ]);

        // Process features
        $features = [];
        foreach ($validated['features'] as $feature) {
            if (!empty($feature['title']) && !empty($feature['description'])) {
                $features[] = [
                    'icon' => $feature['icon'] ?? 'fas fa-check',
                    'icon_color' => $feature['icon_color'] ?? '#D4AF37',
                    'title' => $feature['title'],
                    'description' => $feature['description']
                ];
            }
        }

        $updateData = [
            'title' => $validated['title'],
            'subtitle' => $validated['subtitle'],
            'content' => $validated['content'],
            'is_active' => $request->has('is_active'),
            'background_color' => $validated['background_color'],
            'additional_data' => ['features' => $features]
        ];

        $whyChooseUs->update($updateData);

        return redirect()->route('admin.why-choose-us.index')->with('success', 'Why Choose Us section updated successfully');
    }

    public function toggleStatus()
    {
        $whyChooseUs = HomeContent::where('section_name', $this->sectionName)->first();
        
        if ($whyChooseUs) {
            $whyChooseUs->update(['is_active' => !$whyChooseUs->is_active]);
            $status = $whyChooseUs->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.why-choose-us.index')->with('success', "Why Choose Us section {$status} successfully");
        }
        
        return redirect()->route('admin.why-choose-us.index')->with('error', 'Why Choose Us section not found');
    }
}
