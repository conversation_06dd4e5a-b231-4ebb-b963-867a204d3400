<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HeroSectionController extends Controller
{
    /**
     * Display the hero section management page.
     */
    public function index()
    {
        $heroSection = HomeContent::where('section_name', 'hero_section')->first();
        
        if (!$heroSection) {
            // Create default hero section if it doesn't exist
            $heroSection = HomeContent::create([
                'section_name' => 'hero_section',
                'item_type' => 'hero',
                'title' => 'Welcome to Hestia Abodes',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'content' => '<p>Discover premium residential and commercial properties with Hestia Abodes.</p>',
                'button_text' => 'Explore Properties',
                'button_url' => '/projects',
                'sort_order' => 1,
                'is_active' => true,
            ]);
        }
        
        return view('admin.hero-section.index', compact('heroSection'));
    }

    /**
     * Show the form for editing the hero section.
     */
    public function edit()
    {
        $heroSection = HomeContent::where('section_name', 'hero_section')->first();
        
        if (!$heroSection) {
            return redirect()->route('admin.hero-section.index');
        }
        
        return view('admin.hero-section.edit', compact('heroSection'));
    }

    /**
     * Update the hero section.
     */
    public function update(Request $request)
    {
        $heroSection = HomeContent::where('section_name', 'hero_section')->first();
        
        if (!$heroSection) {
            return redirect()->route('admin.hero-section.index')->with('error', 'Hero section not found.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|string|max:255',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'background_type' => 'nullable|string|in:color,image',
            'background_color' => 'nullable|string|max:7',
        ]);

        $data = $request->except(['background_image', 'images']);
        $data['is_active'] = $request->has('is_active');

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            // Delete old background image if exists
            if ($heroSection->background_image) {
                Storage::disk('public')->delete($heroSection->background_image);
            }

            $backgroundImagePath = $request->file('background_image')->store('hero-section/backgrounds', 'public');
            $data['background_image'] = $backgroundImagePath;
        }

        // Handle multiple images upload
        if ($request->hasFile('images')) {
            // Delete old images from storage
            if ($heroSection->images && is_array($heroSection->images)) {
                foreach ($heroSection->images as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            // Store new images
            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('hero-section', 'public');
            }
            $data['images'] = $images;
        } else {
            // Keep existing images if no new images uploaded
            unset($data['images']);
        }

        $heroSection->update($data);
        
        return redirect()->route('admin.hero-section.index')->with('success', 'Hero section updated successfully');
    }

    /**
     * Toggle hero section status.
     */
    public function toggleStatus()
    {
        $heroSection = HomeContent::where('section_name', 'hero_section')->first();
        
        if ($heroSection) {
            $heroSection->update(['is_active' => !$heroSection->is_active]);
            $status = $heroSection->is_active ? 'activated' : 'deactivated';
            return redirect()->route('admin.hero-section.index')->with('success', "Hero section {$status} successfully");
        }
        
        return redirect()->route('admin.hero-section.index')->with('error', 'Hero section not found');
    }
}
