<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Slider;
use App\Models\ContactEnquiry;

class HomeController extends Controller
{
    public function index()
    {
        // Since we removed slider types, we'll get all active sliders
        // You can modify this logic based on your new requirements
        $sliders = Slider::active()
            ->ordered()
            ->get();

        // For backward compatibility with the view, we'll use the same variable names
        // but you might want to update your view to use a single slider collection
        $heroSliders = $sliders; // All sliders can be used as hero sliders

        // Get projects marked for feature slider
        $featuredProjectSliders = \App\Models\Project::active()
            ->featureSlider()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get testimonials for testimonial slider
        $testimonialSliders = \App\Models\Testimonial::active()
            ->ordered()
            ->get();

        // Get home content sections
        $homeContent = \App\Models\HomeContent::active()
            ->ordered()
            ->get()
            ->keyBy('section_name');

        // Get 4 projects for home page display
        $homeProjects = \App\Models\Project::active()
            ->orderBy('featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(4)
            ->get();

        // Get services for home page display
        $homeServices = \App\Models\Service::active()
            ->ordered()
            ->get();

        // Debug: Check if why_choose_us is loaded
        \Log::info('Home content sections loaded:', $homeContent->keys()->toArray());
        if (isset($homeContent['why_choose_us'])) {
            \Log::info('Why choose us data:', $homeContent['why_choose_us']->toArray());
        } else {
            \Log::error('Why choose us section not found in home content');
        }

        return view('home', compact('heroSliders', 'featuredProjectSliders', 'testimonialSliders', 'homeContent', 'homeProjects', 'homeServices'));
    }

    public function projects()
    {
        // Fetch active projects from database
        $projects = \App\Models\Project::active()
            ->orderBy('featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get unique cities and project types for filters
        $cities = \App\Models\Project::active()
            ->distinct()
            ->pluck('city')
            ->filter();

        $projectTypes = \App\Models\Project::active()
            ->distinct()
            ->pluck('project_type')
            ->filter();

        return view('projects', compact('projects', 'cities', 'projectTypes'));
    }

    public function contact()
    {
        // You can add contact page data fetching logic here
        // For example: office locations, contact forms, etc.

        return view('contact');
    }

    public function aboutUs()
    {
        // Fetch active About Us sections from database
        $aboutSections = \App\Models\AboutUsSection::active()
            ->ordered()
            ->get();

        // Get testimonials for the about page
        $testimonials = \App\Models\Testimonial::active()
            ->ordered()
            ->take(6)
            ->get();

        // Get active founders
        $founders = \App\Models\Founder::active()
            ->ordered()
            ->get();

        return view('about-us', compact('aboutSections', 'testimonials', 'founders'));
    }

    public function blog()
    {
        // You can add blog page data fetching logic here
        // For example: fetch blog posts from database, categories, featured posts, etc.

        return view('blog');
    }

    public function mediaCenter()
    {
        // You can add media center data fetching logic here
        // For example: fetch press releases, news articles, awards, events, etc.

        return view('media-center');
    }

    public function services()
    {
        // Fetch active services from database
        $services = \App\Models\Service::active()
            ->ordered()
            ->get();

        // Get testimonials for the services page
        $testimonials = \App\Models\Testimonial::active()
            ->ordered()
            ->take(6)
            ->get();

        return view('services', compact('services', 'testimonials'));
    }

    public function projectDetails($slug)
    {
        // Fetch project by slug from database
        $project = \App\Models\Project::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Get related projects (same city or project type, excluding current project)
        $relatedProjects = \App\Models\Project::where('is_active', true)
            ->where('id', '!=', $project->id)
            ->where(function($query) use ($project) {
                $query->where('city', $project->city)
                      ->orWhere('project_type', $project->project_type);
            })
            ->limit(3)
            ->get();

        return view('project-details', compact('project', 'relatedProjects'));
    }

    public function blogDetails($slug)
    {
        // You can add blog details data fetching logic here
        // For example: fetch blog post by slug from database, related posts, etc.

        // For now, we'll use static data
        $post = [
            'title' => 'Why Pune\'s Western Corridor Is Booming',
            'slug' => $slug,
            'author' => 'Akmal Raza',
            'date' => 'December 15, 2024'
        ];

        return view('blog-details', compact('post'));
    }

    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'interest' => 'required|string|max:255',
            'budget' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'message' => 'required|string',
            'newsletter' => 'nullable|boolean'
        ]);

        // Create contact enquiry
        ContactEnquiry::create([
            'name' => $validated['firstName'] . ' ' . $validated['lastName'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'subject' => 'Contact Form - ' . ucfirst($validated['interest']),
            'message' => $validated['message'],
            'enquiry_type' => $validated['interest'],
            'status' => 'new',
            'source' => 'website_contact_form',
            'additional_data' => [
                'budget' => $validated['budget'] ?? null,
                'location' => $validated['location'] ?? null,
                'newsletter_subscription' => $validated['newsletter'] ?? false,
                'first_name' => $validated['firstName'],
                'last_name' => $validated['lastName']
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message! We will get back to you within 24 hours.'
        ]);
    }
}
