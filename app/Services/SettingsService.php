<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get setting value by key
     */
    public function get($key, $default = null)
    {
        return Setting::get($key, $default);
    }

    /**
     * Set setting value by key
     */
    public function set($key, $value)
    {
        return Setting::set($key, $value);
    }

    /**
     * Get all settings
     */
    public function all()
    {
        return Setting::getAllSettings();
    }

    /**
     * Get settings by category
     */
    public function getByCategory($category)
    {
        return Cache::remember("settings.category.{$category}", 3600, function () use ($category) {
            return Setting::active()
                ->byCategory($category)
                ->ordered()
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Get company information
     */
    public function getCompanyInfo()
    {
        return [
            'name' => $this->get('company_name', config('app.name')),
            'email' => $this->get('company_email'),
            'phone' => $this->get('company_phone'),
            'address' => $this->get('company_address'),
            'logo' => $this->get('company_logo'),
            'description' => $this->get('company_description'),
        ];
    }

    /**
     * Get contact information
     */
    public function getContactInfo()
    {
        return [
            'email' => $this->get('contact_email'),
            'phone' => $this->get('contact_phone'),
            'address' => $this->get('contact_address'),
            'working_hours' => $this->get('working_hours'),
            'map_embed' => $this->get('map_embed'),
        ];
    }

    /**
     * Get social media links
     */
    public function getSocialLinks()
    {
        return [
            'facebook' => $this->get('social_facebook'),
            'twitter' => $this->get('social_twitter'),
            'instagram' => $this->get('social_instagram'),
            'linkedin' => $this->get('social_linkedin'),
            'youtube' => $this->get('social_youtube'),
            'whatsapp' => $this->get('social_whatsapp'),
        ];
    }

    /**
     * Get SEO settings
     */
    public function getSeoSettings()
    {
        return [
            'meta_title' => $this->get('seo_meta_title'),
            'meta_description' => $this->get('seo_meta_description'),
            'meta_keywords' => $this->get('seo_meta_keywords'),
            'google_analytics' => $this->get('seo_google_analytics'),
            'google_tag_manager' => $this->get('seo_google_tag_manager'),
            'facebook_pixel' => $this->get('seo_facebook_pixel'),
        ];
    }

    /**
     * Get email settings
     */
    public function getEmailSettings()
    {
        return [
            'from_name' => $this->get('email_from_name'),
            'from_email' => $this->get('email_from_email'),
            'reply_to' => $this->get('email_reply_to'),
            'admin_email' => $this->get('email_admin_email'),
        ];
    }

    /**
     * Get appearance settings
     */
    public function getAppearanceSettings()
    {
        return [
            'primary_color' => $this->get('appearance_primary_color', '#007bff'),
            'secondary_color' => $this->get('appearance_secondary_color', '#6c757d'),
            'logo' => $this->get('appearance_logo'),
            'favicon' => $this->get('appearance_favicon'),
            'footer_text' => $this->get('appearance_footer_text'),
        ];
    }

    /**
     * Get feature settings
     */
    public function getFeatureSettings()
    {
        return [
            'enable_testimonials' => $this->get('feature_enable_testimonials', true),
            'enable_projects' => $this->get('feature_enable_projects', true),
            'enable_contact_form' => $this->get('feature_enable_contact_form', true),
            'enable_newsletter' => $this->get('feature_enable_newsletter', false),
            'maintenance_mode' => $this->get('feature_maintenance_mode', false),
        ];
    }

    /**
     * Update multiple settings at once
     */
    public function updateMultiple(array $settings)
    {
        foreach ($settings as $key => $value) {
            $this->set($key, $value);
        }

        // Clear all cache
        Setting::clearCache();
    }

    /**
     * Clear all settings cache
     */
    public function clearCache()
    {
        Setting::clearCache();
    }

    /**
     * Check if a feature is enabled
     */
    public function isFeatureEnabled($feature)
    {
        return (bool) $this->get("feature_enable_{$feature}", false);
    }

    /**
     * Get setting with type casting
     */
    public function getTyped($key, $default = null)
    {
        $setting = Setting::where('key', $key)->where('is_active', true)->first();
        
        if (!$setting) {
            return $default;
        }

        $value = $setting->value;

        switch ($setting->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float) $value : $default;
            case 'file':
                return $value ? asset('storage/' . $value) : $default;
            default:
                return $value ?: $default;
        }
    }
}
