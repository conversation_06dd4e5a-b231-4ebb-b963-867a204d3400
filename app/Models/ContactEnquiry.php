<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContactEnquiry extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'enquiry_type',
        'preferred_contact',
        'status',
        'admin_notes',
        'contacted_at',
        'assigned_to',
        'source',
        'additional_data'
    ];

    protected $casts = [
        'additional_data' => 'array',
        'contacted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function assignedAdmin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'assigned_to');
    }

    // Scopes
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('enquiry_type', $type);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'new' => '<span class="badge bg-primary">New</span>',
            'contacted' => '<span class="badge bg-info">Contacted</span>',
            'in_progress' => '<span class="badge bg-warning">In Progress</span>',
            'resolved' => '<span class="badge bg-success">Resolved</span>',
            'closed' => '<span class="badge bg-secondary">Closed</span>'
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    public function getEnquiryTypeBadgeAttribute()
    {
        $badges = [
            'general' => '<span class="badge bg-light text-dark">General</span>',
            'property' => '<span class="badge bg-primary">Property</span>',
            'investment' => '<span class="badge bg-success">Investment</span>'
        ];

        return $badges[$this->enquiry_type] ?? '<span class="badge bg-secondary">Other</span>';
    }
}
