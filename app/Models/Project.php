<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'location',
        'city',
        'state',
        'developer',
        'project_type',
        'property_types',
        'starting_price',
        'price_range',
        'possession_date',
        'rera_id',
        'total_area',
        'total_units',
        'status',
        'featured',
        'feature_slider',
        'sort_order',
        'images',
        'floor_plans',
        'amenities',
        'specifications',
        'meta_title',
        'meta_description',
        'is_active',
        'background_type',
        'background_color',
        'background_image',
        'brochure_file',
        'icon_color',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'starting_price' => 'decimal:2',
            'total_area' => 'decimal:2',
            'total_units' => 'integer',
            'featured' => 'boolean',
            'feature_slider' => 'boolean',
            'sort_order' => 'integer',
            'is_active' => 'boolean',
            'images' => 'array',
            'floor_plans' => 'array',
            'amenities' => 'array',
            'specifications' => 'array',
        ];
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get formatted starting price
     */
    public function getFormattedStartingPriceAttribute(): string
    {
        return '₹' . number_format($this->starting_price / 10000000, 2) . ' Cr';
    }

    /**
     * Get project URL for frontend
     */
    public function getProjectUrlAttribute(): string
    {
        return route('project-details', $this->slug);
    }

    /**
     * Get brochure URL
     */
    public function getBrochureUrlAttribute(): ?string
    {
        return $this->brochure_file ? asset('storage/' . $this->brochure_file) : null;
    }

    /**
     * Check if project has brochure
     */
    public function hasBrochure(): bool
    {
        return !empty($this->brochure_file);
    }

    /**
     * Scope for active projects
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured projects
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for feature slider projects
     */
    public function scopeFeatureSlider($query)
    {
        return $query->where('feature_slider', true);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope by city
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }
}
