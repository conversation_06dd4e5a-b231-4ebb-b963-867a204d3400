<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VisitSchedule extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'property_type',
        'location_preference',
        'budget_range',
        'preferred_date',
        'preferred_time',
        'alternate_date',
        'alternate_time',
        'special_requirements',
        'status',
        'confirmed_at',
        'scheduled_date',
        'scheduled_time',
        'assigned_agent',
        'agent_notes',
        'visit_type',
        'additional_info'
    ];

    protected $casts = [
        'preferred_date' => 'date',
        'preferred_time' => 'datetime:H:i',
        'alternate_date' => 'date',
        'alternate_time' => 'datetime:H:i',
        'scheduled_date' => 'date',
        'scheduled_time' => 'datetime:H:i',
        'confirmed_at' => 'datetime',
        'additional_info' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function assignedAgent(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'assigned_agent');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_date', '>=', Carbon::today())
                    ->where('status', 'confirmed');
    }

    public function scopeToday($query)
    {
        return $query->where('scheduled_date', Carbon::today());
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => '<span class="badge bg-warning">Pending</span>',
            'confirmed' => '<span class="badge bg-success">Confirmed</span>',
            'completed' => '<span class="badge bg-primary">Completed</span>',
            'cancelled' => '<span class="badge bg-danger">Cancelled</span>',
            'rescheduled' => '<span class="badge bg-info">Rescheduled</span>'
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    public function getVisitTypeBadgeAttribute()
    {
        $badges = [
            'property_viewing' => '<span class="badge bg-primary">Property Viewing</span>',
            'consultation' => '<span class="badge bg-info">Consultation</span>',
            'site_visit' => '<span class="badge bg-success">Site Visit</span>'
        ];

        return $badges[$this->visit_type] ?? '<span class="badge bg-secondary">Other</span>';
    }

    public function getFormattedScheduledDateTimeAttribute()
    {
        if ($this->scheduled_date && $this->scheduled_time) {
            return Carbon::parse($this->scheduled_date->format('Y-m-d') . ' ' . $this->scheduled_time->format('H:i:s'))
                        ->format('M d, Y \a\t g:i A');
        }
        return null;
    }
}
