<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'section_name',
        'item_type',
        'title',
        'subtitle',
        'content',
        'image',
        'icon',
        'images',
        'button_text',
        'button_url',
        'sort_order',
        'is_active',
        'background_type',
        'background_color',
        'background_image',
        'icon_color',
        'additional_data',
        'meta_data',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'images' => 'array',
        'additional_data' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Scope for active content
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered content
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the first image URL
     */
    public function getFirstImageUrlAttribute()
    {
        if ($this->images && is_array($this->images) && count($this->images) > 0) {
            return asset('storage/' . $this->images[0]);
        }
        return null;
    }

    /**
     * Get all image URLs
     */
    public function getImageUrlsAttribute()
    {
        if ($this->images && is_array($this->images)) {
            return array_map(function($image) {
                return asset('storage/' . $image);
            }, $this->images);
        }
        return [];
    }

    /**
     * Get available section types
     */
    public static function getSectionTypes()
    {
        return [
            'hero' => 'Hero Section',
            'about' => 'About Section',
            'services' => 'Services Section',
            'features' => 'Features Section',
            'testimonials' => 'Testimonials Section',
            'stats' => 'Statistics Section',
            'cta' => 'Call to Action',
            'gallery' => 'Image Gallery',
            'team' => 'Team Section',
            'faq' => 'FAQ Section',
            'contact' => 'Contact Section',
            'custom' => 'Custom Section',
        ];
    }

    /**
     * Get section type label
     */
    public function getSectionTypeLabelAttribute()
    {
        $types = self::getSectionTypes();
        return $types[$this->section_name] ?? $this->section_name;
    }
}
