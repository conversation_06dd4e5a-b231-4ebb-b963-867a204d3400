<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'contact_info',
        'phone',
        'email',
        'address',
        'working_hours',
        'website',
        'background_type',
        'background_color',
        'background_image',
        'icon_color',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'contact_info' => 'array',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope to get only active sections
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get available section types for Contact page
     */
    public static function getSectionTypes()
    {
        return [
            'contact_info' => 'Contact Information',
            'office_locations' => 'Office Locations',
            'contact_form' => 'Contact Form',
            'map' => 'Location Map',
            'working_hours' => 'Working Hours',
            'social_media' => 'Social Media Links',
            'emergency_contact' => 'Emergency Contact',
            'departments' => 'Department Contacts',
            'faq' => 'Frequently Asked Questions',
            'custom' => 'Custom Section',
        ];
    }

    /**
     * Get section type label
     */
    public function getSectionTypeLabelAttribute()
    {
        $types = self::getSectionTypes();
        return $types[$this->title] ?? $this->title;
    }
}
