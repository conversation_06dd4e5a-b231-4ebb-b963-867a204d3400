<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category',
        'service_type',
        'icon_class',
        'background_type',
        'background_color',
        'background_image',
        'icon_color',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope for active services
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered services
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get available service categories
     */
    public static function getServiceCategories()
    {
        return [
            'buying' => 'Property Buying',
            'selling' => 'Property Selling',
            'renting' => 'Property Rental',
            'investment' => 'Investment Advisory',
            'legal' => 'Legal Services',
            'valuation' => 'Property Valuation',
            'management' => 'Property Management',
            'consultation' => 'Real Estate Consultation',
            'financing' => 'Financing Assistance',
            'documentation' => 'Documentation Services',
            'custom' => 'Custom Service',
        ];
    }

    /**
     * Get service types for different service categories
     */
    public static function getServiceTypes()
    {
        return [
            'primary' => 'Primary Service',
            'secondary' => 'Secondary Service',
            'premium' => 'Premium Service',
            'consultation' => 'Consultation Only',
        ];
    }
}
