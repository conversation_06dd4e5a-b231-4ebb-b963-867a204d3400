@extends('layouts.app')

@section('title', '{{ $project->meta_title ?: $project->name . " - " . $project->project_type . " Project - Hestia Abodes" }}')
@section('description', '{{ $project->meta_description ?: "Discover " . $project->name . ", a " . strtolower($project->project_type) . " project in " . $project->location . ", " . $project->city . ". Explore details, pricing, and book your dream property." }}')

@section('content')
<!-- Project Hero Section -->
<section class="project-hero">
    <div class="project-hero-slider">
        <div class="project-slide active" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
            <div class="project-hero-overlay"></div>
        </div>
        <div class="project-slide" style="background-image: url('{{ asset('images/default-property.jpg') }}')">
            <div class="project-hero-overlay"></div>
        </div>
        <div class="project-slide" style="background-image: url('{{ asset('images/about-image.jpg') }}')">
            <div class="project-hero-overlay"></div>
        </div>
    </div>
    
    <div class="project-hero-content">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="project-info">
                        <div class="project-badges">
                            @if($project->featured)
                                <span class="badge-premium">Featured</span>
                            @endif
                            <span class="badge-premium">{{ $project->project_type }}</span>
                            <span class="badge-status">
                                @if($project->status === 'ready_to_move')
                                    Ready to Move
                                @elseif($project->status === 'upcoming')
                                    Upcoming
                                @elseif($project->status === 'ongoing')
                                    Under Construction
                                @else
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                @endif
                            </span>
                        </div>
                        <h1 class="project-title" style="color:white;">{{ $project->name }}</h1>
                        <p class="project-location" style="color:white;"><i class="fas fa-map-marker-alt"></i> {{ $project->location }}, {{ $project->city }}, {{ $project->state }}</p>
                        <div class="project-highlights">
                            <div class="highlight-item">
                                <span class="highlight-label">Configuration</span>
                                <span class="highlight-value">{{ $project->property_types }}</span>
                            </div>
                            <div class="highlight-item">
                                <span class="highlight-label">Possession</span>
                                <span class="highlight-value">{{ $project->possession_date }}</span>
                            </div>
                            <div class="highlight-item">
                                <span class="highlight-label">Starting Price</span>
                                <span class="highlight-value">{{ $project->formatted_starting_price }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="project-cta-card">
                        <h4>Interested in {{ $project->name }}?</h4>
                        <p>Get detailed brochure and floor plans</p>
                        @if($project->hasBrochure())
                            <a href="{{ $project->brochure_url }}" target="_blank" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-download"></i> Download Brochure
                            </a>
                        @else
                            <button class="btn btn-secondary btn-lg w-100 mb-3" disabled>
                                <i class="fas fa-file-pdf"></i> Brochure Coming Soon
                            </button>
                        @endif
                        <a href="{{ route('contact') }}#schedule-visit" class="btn btn-outline-light btn-lg w-100 mb-3">
                            <i class="fas fa-calendar-alt"></i> Schedule Site Visit
                        </a>
                        <div class="contact-info" >
                            <p style="color:#cfaa13;"><i class="fas fa-phone" ></i> +91 ************</p>
                            <p style="color:#cfaa13;"><i class="fas fa-envelope"></i> <EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hero Navigation -->
    <div class="hero-navigation">
        <button class="hero-nav-btn prev" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="hero-nav-btn next" onclick="changeSlide(1)">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    
    <!-- Hero Indicators -->
    <div class="hero-indicators">
        <span class="indicator active" onclick="currentSlide(1)"></span>
        <span class="indicator" onclick="currentSlide(2)"></span>
        <span class="indicator" onclick="currentSlide(3)"></span>
    </div>
</section>



<!-- Project Overview -->
<section id="overview" class="project-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="project-content">
                    <h2 class="section-title">Project Overview</h2>
                    @if($project->short_description)
                        <p class="lead">{{ $project->short_description }}</p>
                    @endif

                    @if($project->description)
                        <div class="project-description">
                            {!! nl2br(e($project->description)) !!}
                        </div>
                    @else
                        <p>{{ $project->name }} represents modern living in {{ $project->city }}'s most sought-after location. This {{ strtolower($project->project_type) }} project offers thoughtfully designed spaces that blend luxury with functionality.</p>

                        <p>Strategically located in {{ $project->location }}, {{ $project->city }}, {{ $project->name }} provides excellent connectivity to major business hubs, educational institutions, and healthcare facilities. The project features contemporary architecture, world-class amenities, and sustainable living solutions.</p>
                    @endif
                    
                    <div class="project-features">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <i class="fas fa-building" style="color:#cfaa13;"></i>
                                    <div class="feature-content">
                                        <h5>Premium Architecture</h5>
                                        <p>Contemporary design with modern aesthetics</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <i class="fas fa-leaf" style="color:#cfaa13;"></i>
                                    <div class="feature-content">
                                        <h5>Green Living</h5>
                                        <p>Eco-friendly features and sustainable design</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <i class="fas fa-shield-alt" style="color:#cfaa13;"></i>
                                    <div class="feature-content">
                                        <h5>24/7 Security</h5>
                                        <p>Advanced security systems and gated community</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item">
                                    <i class="fas fa-car" style="color:#cfaa13;"></i>
                                    <div class="feature-content">
                                        <h5>Ample Parking</h5>
                                        <p>Dedicated parking spaces for residents</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <div class="project-specs">
                        <h4>Project Specifications</h4>
                        @if($project->total_area)
                            <div class="spec-item">
                                <span class="spec-label">Total Area</span>
                                <span class="spec-value">{{ number_format($project->total_area) }} sq ft</span>
                            </div>
                        @endif
                        @if($project->total_units)
                            <div class="spec-item">
                                <span class="spec-label">Total Units</span>
                                <span class="spec-value">{{ $project->total_units }} Units</span>
                            </div>
                        @endif
                        <div class="spec-item">
                            <span class="spec-label">Project Type</span>
                            <span class="spec-value">{{ $project->project_type }}</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Configuration</span>
                            <span class="spec-value">{{ $project->property_types }}</span>
                        </div>
                        @if($project->rera_id)
                            <div class="spec-item">
                                <span class="spec-label">RERA ID</span>
                                <span class="spec-value">{{ $project->rera_id }}</span>
                            </div>
                        @endif
                        <div class="spec-item">
                            <span class="spec-label">Possession</span>
                            <span class="spec-value">{{ $project->possession_date }}</span>
                        </div>
                    </div>
                    
                    <div class="developer-info">
                        <h4>Developer</h4>
                        <div class="developer-card">
                            <img src="{{ asset('images/default-property.jpg') }}" alt="{{ $project->developer }}" class="developer-logo">
                            <div class="developer-details">
                                <h5>{{ $project->developer }}</h5>
                                <p>Trusted name in real estate development</p>
                                <a href="{{ route('contact') }}" class="btn btn-outline-primary btn-sm">Contact Developer</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Floor Plans -->
<section id="floor-plans" class="project-section section-padding bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Floor Plans</h2>
            <p class="section-subtitle">Choose from our thoughtfully designed floor plans</p>
        </div>
        
        <div class="floor-plan-tabs">
            <button class="plan-tab active" data-plan="1bhk">1 BHK</button>
            <button class="plan-tab" data-plan="2bhk">2 BHK</button>
            <button class="plan-tab" data-plan="3bhk">3 BHK</button>
        </div>
        
        <div class="floor-plan-content">
            <div class="plan-details active" id="1bhk">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="plan-image">
                            <img src="{{ asset('images/default-property.jpg') }}" alt="1 BHK Floor Plan" class="img-fluid">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="plan-info">
                            <h3>1 BHK Apartment</h3>
                            <div class="plan-specs">
                                <div class="spec-row">
                                    <span class="spec-label">Carpet Area</span>
                                    <span class="spec-value">485 - 520 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Built-up Area</span>
                                    <span class="spec-value">650 - 695 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Price Range</span>
                                    <span class="spec-value">{{ $project->price_range }}</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Balcony</span>
                                    <span class="spec-value">1 Balcony</span>
                                </div>
                            </div>
                            <div class="plan-actions">
                                <a href="#" class="btn btn-primary">Download Floor Plan</a>
                                <a href="#" class="btn btn-outline-primary">Calculate EMI</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="plan-details" id="2bhk">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="plan-image">
                            <img src="{{ asset('images/about-image.jpg') }}" alt="2 BHK Floor Plan" class="img-fluid">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="plan-info">
                            <h3>2 BHK Apartment</h3>
                            <div class="plan-specs">
                                <div class="spec-row">
                                    <span class="spec-label">Carpet Area</span>
                                    <span class="spec-value">785 - 850 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Built-up Area</span>
                                    <span class="spec-value">1050 - 1135 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Price Range</span>
                                    <span class="spec-value">{{ $project->price_range }}</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Balcony</span>
                                    <span class="spec-value">2 Balconies</span>
                                </div>
                            </div>
                            <div class="plan-actions">
                                <a href="#" class="btn btn-primary">Download Floor Plan</a>
                                <a href="#" class="btn btn-outline-primary">Calculate EMI</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="plan-details" id="3bhk">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="plan-image">
                            <img src="{{ asset('images/hero-bg.jpg') }}" alt="3 BHK Floor Plan" class="img-fluid">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="plan-info">
                            <h3>3 BHK Apartment</h3>
                            <div class="plan-specs">
                                <div class="spec-row">
                                    <span class="spec-label">Carpet Area</span>
                                    <span class="spec-value">1185 - 1285 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Built-up Area</span>
                                    <span class="spec-value">1585 - 1720 sq.ft.</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Price Range</span>
                                    <span class="spec-value">{{ $project->price_range }}</span>
                                </div>
                                <div class="spec-row">
                                    <span class="spec-label">Balcony</span>
                                    <span class="spec-value">3 Balconies</span>
                                </div>
                            </div>
                            <div class="plan-actions">
                                <a href="#" class="btn btn-primary">Download Floor Plan</a>
                                <a href="#" class="btn btn-outline-primary">Calculate EMI</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Amenities -->
<section id="amenities" class="project-section section-padding">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">World-Class Amenities</h2>
            <p class="section-subtitle">Experience luxury living with premium facilities</p>
        </div>
        
        <div class="amenities-grid">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-swimming-pool"></i>
                        </div>
                        <h5>Swimming Pool</h5>
                        <p>Temperature controlled swimming pool with kids area</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <h5>Fitness Center</h5>
                        <p>Fully equipped gymnasium with modern equipment</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-tree"></i>
                        </div>
                        <h5>Landscaped Gardens</h5>
                        <p>Beautiful green spaces and walking trails</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <h5>Kids Play Area</h5>
                        <p>Safe and fun play area for children</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5>Clubhouse</h5>
                        <p>Multi-purpose hall for events and gatherings</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <h5>Covered Parking</h5>
                        <p>Dedicated parking spaces for all residents</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5>24/7 Security</h5>
                        <p>Round-the-clock security with CCTV surveillance</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="amenity-card">
                        <div class="amenity-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h5>Power Backup</h5>
                        <p>100% power backup for uninterrupted living</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="project-contact section-padding bg-primary">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mx-auto text-center">
                <h2 class="text-white mb-4">Interested in {{ $project->name }}?</h2>
                <p class="text-white mb-4">Get in touch with our experts for detailed information and site visit</p>
                <div class="contact-actions">
                    @if($project->hasBrochure())
                        <a href="{{ $project->brochure_url }}" target="_blank" class="btn btn-light btn-lg me-3" style="color:#cfaa13!important;">
                            <i class="fas fa-download"></i> Download Brochure
                        </a>
                    @endif
                    <a href="{{ route('contact') }}#schedule-visit" class="btn btn-outline-light btn-lg me-3">
                        <i class="fas fa-calendar-alt"></i> Schedule Visit
                    </a>
                    <a href="tel:+919067881848" class="btn btn-outline-light btn-lg" >
                        <i class="fas fa-phone"></i> Call Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects->count() > 0)
<section class="related-projects section-padding bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Related Projects</h2>
            <p class="section-subtitle">Explore other projects you might be interested in</p>
        </div>

        <div class="row">
            @foreach($relatedProjects as $relatedProject)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="{{ asset('images/default-property.jpg') }}" alt="{{ $relatedProject->name }}" class="img-fluid">
                            <div class="project-overlay">
                                <div class="project-status">{{ $relatedProject->project_type }}</div>
                                <div class="project-actions">
                                    <a href="{{ route('project-details', $relatedProject->slug) }}" class="btn btn-primary btn-sm">View Details</a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <h4 class="project-title">{{ $relatedProject->name }}</h4>
                            <p class="project-location">
                                <i class="fas fa-map-marker-alt"></i> {{ $relatedProject->location }}, {{ $relatedProject->city }}
                            </p>
                            <div class="project-details">
                                <div class="detail-item">
                                    <span class="detail-label">Configuration:</span>
                                    <span class="detail-value">{{ $relatedProject->property_types }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Starting Price:</span>
                                    <span class="detail-value price">{{ $relatedProject->formatted_starting_price }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

@endsection
