@extends('layouts.app')

@section('title', 'Hestia Abodes - Premium Real Estate Solutions')
@section('description', 'Discover premium residential and commercial properties with Hestia Abodes. Your trusted partner in real estate investment and property management.')

@section('content')
<!-- Hero Slider -->
<section class="hero-slider swiper">
    <div class="swiper-wrapper">
        @if($heroSliders->count() > 0)
            @foreach($heroSliders as $slider)
                <div class="swiper-slide hero-slide" style="background-image: url('{{ $slider->image_url }}')">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4">{{ $slider->title }}</h1>
                        @if($slider->subtitle)
                            <h2 class="h4 mb-4 text-light">{{ $slider->subtitle }}</h2>
                        @endif
                        @if($slider->description)
                            <p class="lead mb-4">{{ $slider->description }}</p>
                        @endif
                        <div class="hero-buttons">
                            @if($slider->button_text && $slider->button_link)
                                <a href="{{ $slider->button_link }}" class="btn btn-primary btn-lg me-3">{{ $slider->button_text }}</a>
                            @endif
                            @if($slider->button_text_2 && $slider->button_link_2)
                                <a href="{{ $slider->button_link_2 }}" class="btn btn-outline-light btn-lg">{{ $slider->button_text_2 }}</a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- Fallback content if no sliders exist -->
            <div class="swiper-slide hero-slide" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">Hestia Abodes </h1>
                    <p class="lead mb-4">Whether you're a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.</p>
                    <a href="{{ route('projects') }}" class="btn btn-primary btn-lg me-3">Explore Projects</a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">Get in Touch</a>
                </div>
            </div>
        @endif
    </div>
    <div class="swiper-pagination"></div>
    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
</section>

<!-- Who We Are Section -->
@if(isset($homeContent['about_preview']))
<section class="who-we-are section-padding">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="about-image-wrapper">
                    @if($homeContent['about_preview']->image)
                        <img src="{{ asset('storage/' . $homeContent['about_preview']->image) }}" alt="{{ $homeContent['about_preview']->title }}" class="img-fluid rounded-3 shadow-lg">
                    @else
                        <img src="{{ asset('images/about-image.jpg') }}" alt="Hestia Abodes Team" class="img-fluid rounded-3 shadow-lg">
                    @endif
                    <div class="experience-badge d-none">
                        <div class="badge-content">
                            <h3>5+</h3>
                            <p>Years of<br>Excellence</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content pt-5">
                    <h2 class="section-title">{{ $homeContent['about_preview']->title }}</h2>
                    @if($homeContent['about_preview']->subtitle)
                        <p class="lead">{{ $homeContent['about_preview']->subtitle }}</p>
                    @endif

                    <div class="content">
                        {!! $homeContent['about_preview']->content !!}
                    </div>

                    <div class="core-values">
                        <div class="value-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Integrity</span>
                        </div>
                        <div class="value-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Insight</span>
                        </div>
                        <div class="value-item">
                            <i class="fas fa-bullseye"></i>
                            <span>Impact</span>
                        </div>
                    </div>

                    @if($homeContent['about_preview']->button_text && $homeContent['about_preview']->button_url)
                        <a href="{{ $homeContent['about_preview']->button_url }}" class="btn btn-primary mt-4">{{ $homeContent['about_preview']->button_text }}</a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Featured Projects Full Screen -->
<section id="featured" class="section-padding bg-light">
    <!-- Section Title Overlay -->
     <div class="text-center mb-5">
        <div class="section-title">
            <h2>FEATURED PROJECTS</h2>
        </div>
    </div>
    <div class="featured-projects-slider swiper" style="height:800px;">
        <div class="swiper-wrapper">
            <!-- Project 1: Luxury Villa in Goa -->
            <div class="swiper-slide featured-project-slide" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
                <div class="featured-project-content">
                    <div class="featured-project-subtitle">Premium Residential</div>
                    <h2 class="featured-project-title">Godrej Meridien</h2>

                    <div class="project-details">
                        <div class="project-detail-item">
                            <span class="project-detail-label">Sector 106, Gurgaon</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Possession</span>
                            <span class="project-detail-value">January 2025</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Configuration</span>
                            <span class="project-detail-value">1, 2 & 3 BHK</span>
                        </div>
                    </div>

                    <div class="project-price">INR 1.62 Cr onwards</div>

                    <a href="#" class="project-action-btn">
                        <i class="fas fa-info-circle"></i> Know More
                    </a>
                </div>
            </div>

            <!-- Project 2: Modern Apartment -->
            <div class="swiper-slide featured-project-slide" style="background-image: url('{{ asset('images/default-property.jpg') }}')">
                <div class="featured-project-content">
                    <div class="featured-project-subtitle">Urban Living</div>
                    <h2 class="featured-project-title">M3M Heights</h2>

                    <div class="project-details">
                        <div class="project-detail-item">
                            <span class="project-detail-label">Sector 65, Gurgaon</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Possession</span>
                            <span class="project-detail-value">Ready to Move</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Configuration</span>
                            <span class="project-detail-value">2, 3 & 4 BHK</span>
                        </div>
                    </div>

                    <div class="project-price">INR 2.85 Cr onwards</div>

                    <a href="#" class="project-action-btn">
                        <i class="fas fa-info-circle"></i> Know More
                    </a>
                </div>
            </div>

            <!-- Project 3: Commercial Space -->
            <div class="swiper-slide featured-project-slide" style="background-image: url('{{ asset('images/about-image.jpg') }}')">
                <div class="featured-project-content">
                    <div class="featured-project-subtitle">Commercial Excellence</div>
                    <h2 class="featured-project-title">DLF Corporate Greens</h2>

                    <div class="project-details">
                        <div class="project-detail-item">
                            <span class="project-detail-label">Sector 74A, Gurgaon</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Possession</span>
                            <span class="project-detail-value">December 2024</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Configuration</span>
                            <span class="project-detail-value">Office Spaces</span>
                        </div>
                    </div>

                    <div class="project-price">INR 1.25 Cr onwards</div>

                    <a href="#" class="project-action-btn">
                        <i class="fas fa-info-circle"></i> Know More
                    </a>
                </div>
            </div>

            <!-- Project 4: Luxury Penthouse -->
            <div class="swiper-slide featured-project-slide" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
                <div class="featured-project-content">
                    <div class="featured-project-subtitle">Ultra Luxury</div>
                    <h2 class="featured-project-title">Emaar Palm Hills</h2>

                    <div class="project-details">
                        <div class="project-detail-item">
                            <span class="project-detail-label">Sector 77, Gurgaon</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Possession</span>
                            <span class="project-detail-value">March 2026</span>
                        </div>
                        <div class="project-detail-item">
                            <span class="project-detail-label">Configuration</span>
                            <span class="project-detail-value">3, 4 & 5 BHK</span>
                        </div>
                    </div>

                    <div class="project-price">INR 3.75 Cr onwards</div>

                    <a href="#" class="project-action-btn">
                        <i class="fas fa-info-circle"></i> Know More
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="featured-projects-nav">
            <div class="featured-nav-btn swiper-button-prev">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="featured-nav-btn swiper-button-next">
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>

        <!-- Pagination -->
        <div class="featured-projects-pagination swiper-pagination"></div>
    </div>
</section>

<!-- Why Choose Us -->
@if(isset($homeContent['why_choose_us']) && $homeContent['why_choose_us']->is_active)
    @php
        $whyChooseUs = $homeContent['why_choose_us'];
        $features = $whyChooseUs->additional_data['features'] ?? [];
    @endphp
    <section id="why-us" class="section-padding bg-light" @if($whyChooseUs->background_color) style="background-color: {{ $whyChooseUs->background_color }};" @endif>
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">{{ $whyChooseUs->title ?? 'Why Choose Hestia Abodes' }}</h2>
                @if($whyChooseUs->subtitle)
                    <p class="section-subtitle">{{ $whyChooseUs->subtitle }}</p>
                @endif
            </div>

            @if(!empty($features))
                <div class="row">
                    @foreach($features as $feature)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="text-center">
                                <div class="service-icon">
                                    <i class="{{ $feature['icon'] ?? 'fas fa-check' }}" @if(isset($feature['icon_color'])) style="color: {{ $feature['icon_color'] }};" @endif></i>
                                </div>
                                <h4>{{ $feature['title'] }}</h4>
                                <p>{{ $feature['description'] }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif

            @if($whyChooseUs->content)
                <div class="row mt-5">
                    <div class="col-12 text-center">
                        <div class="content-section">
                            {!! $whyChooseUs->content !!}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>
@else
    <!-- Fallback: Show hardcoded content if database content is not available -->
    <section id="why-us" class="section-padding bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Why Choose Hestia Abodes</h2>
                <p class="section-subtitle">Where every decision is backed by trust</p>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4>Selective, Not Generic</h4>
                        <p>We only recommend what fits you. Our curated approach ensures you see properties that truly match your needs and preferences.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h4>We Represent You, Not the Developer</h4>
                        <p>Your goals come first. As independent consultants, we provide unbiased advice focused on your best interests.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Market Insight Over Marketing</h4>
                        <p>We share context, not just content. Get real market analysis and honest feedback, not sales pitches.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h4>Process-Driven</h4>
                        <p>You always know what's happening next. Our systematic approach ensures transparency at every step of your journey.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Compliance-First</h4>
                        <p>We protect your investment with legal clarity. All documentation and RERA compliance thoroughly verified.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="text-center">
                        <div class="service-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h4>Here for the Long Haul</h4>
                        <p>Even after deal closure, we remain your advisors. Long-term relationships matter more than one-time transactions.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endif

<!-- Our Projects -->
<section id="projects" class="section-padding">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Our Projects</h2>
            <p class="section-subtitle">Discover Premium Properties Across Prime Locations</p>
        </div>

        @if($homeProjects->count() > 0)
            <div class="row">
                @foreach($homeProjects as $project)
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card project-card h-100">
                            <div class="project-image-container">
                                @if($project->images && count($project->images) > 0)
                                    <img src="{{ asset('storage/' . $project->images[0]) }}" class="card-img-top" alt="{{ $project->name }}">
                                @else
                                    <img src="{{ asset('images/default-property.jpg') }}" class="card-img-top" alt="{{ $project->name }}">
                                @endif
                                @if($project->featured)
                                    <div class="featured-badge">
                                        <span class="badge bg-warning text-dark">Featured</span>
                                    </div>
                                @endif
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ $project->name }}</h5>
                                <p class="card-text text-muted mb-2">
                                    <i class="fas fa-map-marker-alt"></i> {{ $project->location }}, {{ $project->city }}
                                </p>
                                <p class="card-text mb-2">
                                    <strong>{{ $project->property_types }}</strong>
                                </p>
                                <p class="card-text text-primary mb-3">
                                    <strong>{{ $project->price_range }}</strong>
                                </p>
                                @if($project->short_description)
                                    <p class="card-text flex-grow-1">{{ Str::limit($project->short_description, 80) }}</p>
                                @endif
                                <div class="mt-auto">
                                    <a href="{{ route('project-details', $project->slug) }}" class="btn btn-primary btn-sm me-2">View Details</a>
                                    <a href="{{ route('contact') }}" class="btn btn-outline-primary btn-sm">Schedule Visit</a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="text-center mt-4">
                <a href="{{ route('projects') }}" class="btn btn-primary btn-lg">View All Projects</a>
            </div>
        @else
            <div class="text-center">
                <p class="text-muted">No projects available at the moment.</p>
            </div>
        @endif
    </div>
</section>

<!-- What Our Clients Say -->
<section id="testimonials" class="section-padding bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">What Our Clients Say</h2>
            <p class="section-subtitle">Real Stories from Real Customers</p>
        </div>

        <div class="testimonials-slider swiper">
            <div class="swiper-wrapper">
                @forelse($testimonialSliders as $testimonial)
                    <div class="swiper-slide">
                        <div class="testimonial-card">
                            <div class="mb-3">
                                {!! $testimonial->star_rating !!}
                            </div>
                            <p class="mb-4">"{{ $testimonial->testimonial }}"</p>
                            <img src="{{ $testimonial->image_url }}" alt="{{ $testimonial->name }}" class="testimonial-avatar">
                            <h6 class="mt-3 mb-1">{{ $testimonial->name }}</h6>
                            <small class="text-muted">
                                @if($testimonial->designation)
                                    {{ $testimonial->designation }}
                                    @if($testimonial->company)
                                        , {{ $testimonial->company }}
                                    @endif
                                @endif
                                @if($testimonial->location)
                                    <br>{{ $testimonial->location }}
                                @endif
                            </small>
                        </div>
                    </div>
                @empty
                    <div class="swiper-slide">
                        <div class="testimonial-card">
                            <div class="mb-3">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                            </div>
                            <p class="mb-4">"Great service and professional approach. Highly recommended!"</p>
                            <img src="{{ asset('images/default-avatar.jpg') }}" alt="Client" class="testimonial-avatar">
                            <h6 class="mt-3 mb-1">Happy Client</h6>
                            <small class="text-muted">Customer</small>
                        </div>
                    </div>
                @endforelse
            </div>
            <div class="testimonials-pagination swiper-pagination mt-4"></div>
        </div>
    </div>
</section>

<!-- Our Services -->
@if($homeServices->count() > 0)
<section id="services" class="section-padding">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Our Services</h2>
            <p class="section-subtitle">For Home Buyers – Your Ideal Home, Simplified</p>
        </div>

        <div class="row">
            @foreach($homeServices as $service)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card"
                         @if($service->background_type === 'color' && $service->background_color)
                             style="background-color: {{ $service->background_color }};"
                         @elseif($service->background_type === 'image' && $service->background_image)
                             style="background-image: url('{{ asset('storage/' . $service->background_image) }}'); background-size: cover; background-position: center;"
                         @endif>
                        <div class="service-icon">
                            @if($service->icon_class)
                                <i class="{{ $service->icon_class }}"
                                   @if($service->icon_color) style="color: {{ $service->icon_color }};" @endif></i>
                            @else
                                <i class="fas fa-cog"
                                   @if($service->icon_color) style="color: {{ $service->icon_color }};" @endif></i>
                            @endif
                        </div>
                        <h4>{{ $service->title }}</h4>
                        <p>{!! $service->description !!}</p>
                        <a href="{{ route('services') }}" class="btn btn-outline-warning">Learn More</a>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-center mt-4">
            <a href="{{ route('services') }}" class="btn btn-primary btn-lg">View All Services</a>
        </div>
    </div>
</section>
@endif

<!-- Contact Us Form -->
<!-- Contact Form & Map -->
<section class="contact-form-section section-padding bg-light">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="contact-form-wrapper">
                    <h2 class="section-title">Send Us a Message</h2>
                    <p class="section-subtitle">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
                    
                    <form id="contactForm" class="contact-form">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" name="firstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="interest" class="form-label">I'm Interested In *</label>
                            <select class="form-control" id="interest" name="interest" required>
                                <option value="">Select Your Interest</option>
                                <option value="buying">Buying a Property</option>
                                <option value="selling">Selling a Property</option>
                                <option value="renting">Renting a Property</option>
                                <option value="investment">Investment Opportunities</option>
                                <option value="commercial">Commercial Properties</option>
                                <option value="consultation">Property Consultation</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="budget" class="form-label">Budget Range</label>
                            <select class="form-control" id="budget" name="budget">
                                <option value="">Select Budget Range</option>
                                <option value="under-50">Under ₹50 Lakhs</option>
                                <option value="50-100">₹50 Lakhs - ₹1 Crore</option>
                                <option value="100-200">₹1 Crore - ₹2 Crores</option>
                                <option value="200-500">₹2 Crores - ₹5 Crores</option>
                                <option value="above-500">Above ₹5 Crores</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="location" class="form-label">Preferred Location</label>
                            <input type="text" class="form-control" id="location" name="location" placeholder="e.g., Pune, Mumbai, Gurgaon">
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" placeholder="Tell us about your requirements..." required></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                            <label class="form-check-label" for="newsletter">
                                I would like to receive updates about new properties and market insights
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information & Map -->
            <div class="col-lg-6">
                <div class="contact-info-wrapper">
                    <h3 class="mb-4">Why Choose Hestia Abodes?</h3>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Expert Consultation</h5>
                            <p>Get personalized advice from our experienced real estate professionals.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Trusted Service</h5>
                            <p>98% client satisfaction rate with transparent and reliable service.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Quick Response</h5>
                            <p>We respond to all inquiries within 24 hours, often much sooner.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="feature-content">
                            <h5>End-to-End Support</h5>
                            <p>From property search to final handover, we're with you every step.</p>
                        </div>
                    </div>
                    
                    <!-- Map Placeholder -->
                    <div class="map-container mt-4">
                        <div class="map-placeholder">
                            <i class="fas fa-map-marked-alt"></i>
                            <h5>Find Us in Pune</h5>
                            <p>We're located in the heart of Pune, Maharashtra. Contact us to schedule a visit to our office.</p>
                            <a href="https://maps.google.com" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-directions me-2"></i>Get Directions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
