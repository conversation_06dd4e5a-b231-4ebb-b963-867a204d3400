@extends('admin.layouts.app')

@section('title', 'Edit About Preview')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit About Preview Section</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.about-preview.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to About Preview
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.about-preview.update') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="title">Section Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ old('title', $aboutPreview->title) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="subtitle">Section Subtitle</label>
                                    <input type="text" class="form-control" id="subtitle" name="subtitle" 
                                           value="{{ old('subtitle', $aboutPreview->subtitle) }}">
                                </div>

                                <div class="form-group">
                                    <label for="content">Section Content</label>
                                    <textarea class="form-control ckeditor" id="content" name="content" rows="6">{{ old('content', $aboutPreview->content) }}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="button_text">Button Text</label>
                                            <input type="text" class="form-control" id="button_text" name="button_text" 
                                                   value="{{ old('button_text', $aboutPreview->button_text) }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="button_url">Button URL</label>
                                            <input type="text" class="form-control" id="button_url" name="button_url"
                                                   value="{{ old('button_url', $aboutPreview->button_url) }}"
                                                   placeholder="e.g., /about-us, https://example.com, #section">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {{ old('is_active', $aboutPreview->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Active Section</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="image">Section Image</label>
                                    <input type="file" class="form-control-file" id="image" name="image" accept="image/*">
                                    @if($aboutPreview->image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $aboutPreview->image) }}" 
                                                 alt="Current Image" class="img-fluid rounded" style="max-height: 150px;">
                                            <small class="form-text text-muted">Current image</small>
                                        </div>
                                    @endif
                                </div>

                                <div class="form-group">
                                    <label for="images">Additional Images</label>
                                    <input type="file" class="form-control-file" id="images" name="images[]" accept="image/*" multiple>
                                    @if($aboutPreview->images && is_array($aboutPreview->images) && count($aboutPreview->images) > 0)
                                        <div class="mt-2">
                                            <div class="row">
                                                @foreach($aboutPreview->images as $image)
                                                    <div class="col-6 mb-2">
                                                        <img src="{{ asset('storage/' . $image) }}" 
                                                             alt="Additional Image" class="img-fluid rounded" style="max-height: 80px;">
                                                    </div>
                                                @endforeach
                                            </div>
                                            <small class="form-text text-muted">Current additional images</small>
                                        </div>
                                    @endif
                                </div>

                                <div class="form-group">
                                    <label for="icon">FontAwesome Icon</label>
                                    <input type="text" class="form-control" id="icon" name="icon" 
                                           value="{{ old('icon', $aboutPreview->icon) }}" 
                                           placeholder="e.g., fas fa-info-circle">
                                    <small class="form-text text-muted">Enter FontAwesome icon class</small>
                                </div>

                                <div class="form-group">
                                    <label for="icon_color">Icon Color</label>
                                    <input type="color" class="form-control" id="icon_color" name="icon_color" 
                                           value="{{ old('icon_color', $aboutPreview->icon_color ?? '#007bff') }}">
                                </div>

                                <div class="form-group">
                                    <label for="background_color">Background Color</label>
                                    <input type="color" class="form-control" id="background_color" name="background_color" 
                                           value="{{ old('background_color', $aboutPreview->background_color) }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update About Preview
                        </button>
                        <a href="{{ route('admin.about-preview.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
    ClassicEditor
        .create(document.querySelector('.ckeditor'))
        .catch(error => {
            console.error(error);
        });
</script>
@endsection
