@extends('admin.layouts.app')

@section('title', 'Edit Why Choose Us Section')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 class="admin-card-title">Edit Why Choose Us Section</h3>
                    <div class="admin-card-tools">
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to View
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ route('admin.why-choose-us.update') }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="admin-card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="form-group">
                                    <label for="title">Section Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ old('title', $whyChooseUs->title) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="subtitle">Section Subtitle</label>
                                    <input type="text" class="form-control" id="subtitle" name="subtitle" 
                                           value="{{ old('subtitle', $whyChooseUs->subtitle) }}">
                                </div>

                                <div class="form-group">
                                    <label for="content">Section Content</label>
                                    <textarea class="form-control ckeditor" id="content" name="content" rows="4">{{ old('content', $whyChooseUs->content) }}</textarea>
                                </div>

                                <!-- Features Section -->
                                <div class="features-section">
                                    <h5>Features</h5>
                                    <div id="features-container">
                                        @php
                                            $features = old('features', $whyChooseUs->additional_data['features'] ?? []);
                                            if (empty($features)) {
                                                $features = [
                                                    [
                                                        'icon' => 'fas fa-check',
                                                        'icon_color' => '#D4AF37',
                                                        'title' => '',
                                                        'description' => ''
                                                    ]
                                                ];
                                            }
                                        @endphp
                                        
                                        @foreach($features as $index => $feature)
                                            <div class="feature-item border p-3 mb-3" data-index="{{ $index }}">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6>Feature {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeFeature(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group">
                                                            <label>FontAwesome Icon Class</label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control"
                                                                       name="features[{{ $index }}][icon]"
                                                                       value="{{ $feature['icon'] ?? 'fas fa-check' }}"
                                                                       placeholder="e.g., fas fa-check"
                                                                       onchange="updatePreview(this)">
                                                                <div class="input-group-append">
                                                                    <button type="button" class="btn btn-outline-secondary" onclick="showIconPicker(this)">
                                                                        <i class="fas fa-icons"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <small class="text-muted">Preview: <i class="{{ $feature['icon'] ?? 'fas fa-check' }} icon-preview" style="color: {{ $feature['icon_color'] ?? '#D4AF37' }};"></i></small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Icon Color</label>
                                                            <input type="color" class="form-control"
                                                                   name="features[{{ $index }}][icon_color]"
                                                                   value="{{ $feature['icon_color'] ?? '#D4AF37' }}"
                                                                   onchange="updatePreview(this)">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Title</label>
                                                    <input type="text" class="form-control" 
                                                           name="features[{{ $index }}][title]" 
                                                           value="{{ $feature['title'] ?? '' }}" required>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Description</label>
                                                    <textarea class="form-control" 
                                                              name="features[{{ $index }}][description]" 
                                                              rows="3" required>{{ $feature['description'] ?? '' }}</textarea>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <button type="button" class="btn btn-success" onclick="addFeature()">
                                        <i class="fas fa-plus"></i> Add Feature
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Settings -->
                                <div class="form-group">
                                    <label for="background_color">Background Color</label>
                                    <input type="color" class="form-control" id="background_color" name="background_color" 
                                           value="{{ old('background_color', $whyChooseUs->background_color) }}">
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {{ old('is_active', $whyChooseUs->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Active Section</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Why Choose Us Section
                        </button>
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Icon Picker Modal -->
<div class="modal fade" id="iconPickerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Choose an Icon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-check')"><i class="fas fa-check"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-star')"><i class="fas fa-star"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-heart')"><i class="fas fa-heart"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-home')"><i class="fas fa-home"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-user')"><i class="fas fa-user"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-users')"><i class="fas fa-users"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-search')"><i class="fas fa-search"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-cog')"><i class="fas fa-cog"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-envelope')"><i class="fas fa-envelope"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-phone')"><i class="fas fa-phone"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-calendar')"><i class="fas fa-calendar"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-clock')"><i class="fas fa-clock"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-shield-alt')"><i class="fas fa-shield-alt"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-handshake')"><i class="fas fa-handshake"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-chart-line')"><i class="fas fa-chart-line"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-list-alt')"><i class="fas fa-list-alt"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-building')"><i class="fas fa-building"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-key')"><i class="fas fa-key"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-award')"><i class="fas fa-award"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-thumbs-up')"><i class="fas fa-thumbs-up"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-lightbulb')"><i class="fas fa-lightbulb"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-rocket')"><i class="fas fa-rocket"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-target')"><i class="fas fa-target"></i></button></div>
                    <div class="col-2 text-center mb-3"><button type="button" class="btn btn-outline-secondary" onclick="selectIcon('fas fa-gem')"><i class="fas fa-gem"></i></button></div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
    .icon-preview {
        font-size: 1.2rem;
        margin-left: 5px;
    }

    .icon-option {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2px;
    }

    .icon-option:hover {
        background-color: #007bff;
        color: white;
    }

    .feature-item {
        background: #f8f9fa;
        border-radius: 8px;
    }

    .input-group-append .btn {
        border-left: 0;
    }
</style>
@endsection

@section('scripts')
@push('scripts')
<script>
// Initialize CKEditor
document.addEventListener('DOMContentLoaded', function() {
    ClassicEditor.create(document.querySelector('.ckeditor'))
        .catch(error => {
            console.error(error);
        });
});

let featureIndex = {{ count($features ?? []) }};

function addFeature() {
    const container = document.getElementById('features-container');
    const featureHtml = `
        <div class="feature-item border p-3 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6>Feature ${featureIndex + 1}</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFeature(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label>FontAwesome Icon Class</label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="features[${featureIndex}][icon]"
                                   value="fas fa-check"
                                   placeholder="e.g., fas fa-check"
                                   onchange="updatePreview(this)">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-outline-secondary" onclick="showIconPicker(this)">
                                    <i class="fas fa-icons"></i>
                                </button>
                            </div>
                        </div>
                        <small class="text-muted">Preview: <i class="fas fa-check icon-preview" style="color: #D4AF37;"></i></small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Icon Color</label>
                        <input type="color" class="form-control"
                               name="features[${featureIndex}][icon_color]"
                               value="#D4AF37"
                               onchange="updatePreview(this)">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>Feature Title</label>
                <input type="text" class="form-control"
                       name="features[${featureIndex}][title]"
                       placeholder="Enter feature title" required>
            </div>

            <div class="form-group">
                <label>Feature Description</label>
                <textarea class="form-control"
                          name="features[${featureIndex}][description]"
                          rows="3" placeholder="Enter feature description" required></textarea>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', featureHtml);
    featureIndex++;
}

function removeFeature(button) {
    if (document.querySelectorAll('.feature-item').length > 1) {
        button.closest('.feature-item').remove();
    } else {
        alert('At least one feature is required.');
    }
}

function updatePreview(input) {
    const featureItem = input.closest('.feature-item');
    const preview = featureItem.querySelector('.icon-preview');
    const iconInput = featureItem.querySelector('input[name*="[icon]"]');
    const colorInput = featureItem.querySelector('input[name*="[icon_color]"]');

    if (preview && iconInput && colorInput) {
        preview.className = iconInput.value + ' icon-preview';
        preview.style.color = colorInput.value;
    }
}

let currentIconInput = null;

function showIconPicker(button) {
    const modalElement = document.getElementById('iconPickerModal');
    const iconInput = button.closest('.input-group').querySelector('input');
    currentIconInput = iconInput;

    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

function selectIcon(iconClass) {
    if (currentIconInput) {
        currentIconInput.value = iconClass;
        updatePreview(currentIconInput);

        const modalElement = document.getElementById('iconPickerModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to existing inputs
    document.querySelectorAll('input[name*="[icon]"], input[name*="[icon_color]"]').forEach(input => {
        input.addEventListener('change', function() {
            updatePreview(this);
        });
        input.addEventListener('input', function() {
            updatePreview(this);
        });
    });
});
</script>
@endpush
