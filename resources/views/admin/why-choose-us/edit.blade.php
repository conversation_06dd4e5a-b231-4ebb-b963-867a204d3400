@extends('admin.layouts.app')

@section('title', 'Edit Why Choose Us Section')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 class="admin-card-title">Edit Why Choose Us Section</h3>
                    <div class="admin-card-tools">
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to View
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ route('admin.why-choose-us.update') }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="admin-card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="form-group">
                                    <label for="title">Section Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ old('title', $whyChooseUs->title) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="subtitle">Section Subtitle</label>
                                    <input type="text" class="form-control" id="subtitle" name="subtitle" 
                                           value="{{ old('subtitle', $whyChooseUs->subtitle) }}">
                                </div>

                                <div class="form-group">
                                    <label for="content">Section Content</label>
                                    <textarea class="form-control ckeditor" id="content" name="content" rows="4">{{ old('content', $whyChooseUs->content) }}</textarea>
                                </div>

                                <!-- Features Section -->
                                <div class="features-section">
                                    <h5>Features</h5>
                                    <div id="features-container">
                                        @php
                                            $features = old('features', $whyChooseUs->additional_data['features'] ?? []);
                                            if (empty($features)) {
                                                $features = [
                                                    [
                                                        'icon' => 'fas fa-check',
                                                        'icon_color' => '#D4AF37',
                                                        'title' => '',
                                                        'description' => ''
                                                    ]
                                                ];
                                            }
                                        @endphp
                                        
                                        @foreach($features as $index => $feature)
                                            <div class="feature-item border p-3 mb-3" data-index="{{ $index }}">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6>Feature {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-danger remove-feature">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group">
                                                            <label>FontAwesome Icon Class</label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control icon-input"
                                                                       name="features[{{ $index }}][icon]"
                                                                       value="{{ $feature['icon'] ?? 'fas fa-check' }}"
                                                                       placeholder="e.g., fas fa-check">
                                                                <div class="input-group-append">
                                                                    <button type="button" class="btn btn-outline-secondary icon-picker-btn">
                                                                        <i class="fas fa-icons"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <small class="text-muted">Preview: <i class="{{ $feature['icon'] ?? 'fas fa-check' }} icon-preview" style="color: {{ $feature['icon_color'] ?? '#D4AF37' }};"></i></small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>Icon Color</label>
                                                            <input type="color" class="form-control icon-color-input"
                                                                   name="features[{{ $index }}][icon_color]"
                                                                   value="{{ $feature['icon_color'] ?? '#D4AF37' }}">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Title</label>
                                                    <input type="text" class="form-control" 
                                                           name="features[{{ $index }}][title]" 
                                                           value="{{ $feature['title'] ?? '' }}" required>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Description</label>
                                                    <textarea class="form-control" 
                                                              name="features[{{ $index }}][description]" 
                                                              rows="3" required>{{ $feature['description'] ?? '' }}</textarea>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <button type="button" class="btn btn-success" id="add-feature">
                                        <i class="fas fa-plus"></i> Add Feature
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Settings -->
                                <div class="form-group">
                                    <label for="background_color">Background Color</label>
                                    <input type="color" class="form-control" id="background_color" name="background_color" 
                                           value="{{ old('background_color', $whyChooseUs->background_color) }}">
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {{ old('is_active', $whyChooseUs->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Active Section</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Why Choose Us Section
                        </button>
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    .icon-preview {
        font-size: 1.2rem;
        margin-left: 5px;
    }

    .icon-option {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2px;
    }

    .icon-option:hover {
        background-color: #007bff;
        color: white;
    }

    .feature-item {
        background: #f8f9fa;
        border-radius: 8px;
    }

    .input-group-append .btn {
        border-left: 0;
    }
</style>
@endsection

@section('scripts')
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor.create(document.querySelector('.ckeditor'))
        .catch(error => {
            console.error(error);
        });

    let featureIndex = {{ count($features ?? []) }};

    // Add new feature
    const addButton = document.getElementById('add-feature');
    if (addButton) {
        addButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Add feature clicked');
            const container = document.getElementById('features-container');
        const featureHtml = `
            <div class="feature-item border p-3 mb-3" data-index="${featureIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6>Feature ${featureIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-feature">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label>FontAwesome Icon Class</label>
                            <div class="input-group">
                                <input type="text" class="form-control icon-input"
                                       name="features[${featureIndex}][icon]"
                                       value="fas fa-check"
                                       placeholder="e.g., fas fa-check">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary icon-picker-btn">
                                        <i class="fas fa-icons"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted">Preview: <i class="fas fa-check icon-preview" style="color: #D4AF37;"></i></small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Icon Color</label>
                            <input type="color" class="form-control icon-color-input"
                                   name="features[${featureIndex}][icon_color]"
                                   value="#D4AF37">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Feature Title</label>
                    <input type="text" class="form-control"
                           name="features[${featureIndex}][title]"
                           value="" required>
                </div>

                <div class="form-group">
                    <label>Feature Description</label>
                    <textarea class="form-control"
                              name="features[${featureIndex}][description]"
                              rows="3" required></textarea>
                </div>
            </div>
        `;

            container.insertAdjacentHTML('beforeend', featureHtml);
            featureIndex++;
            updateFeatureNumbers();
            initializeIconPickers();
        });
    }

    // Remove feature
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-feature')) {
            if (document.querySelectorAll('.feature-item').length > 1) {
                e.target.closest('.feature-item').remove();
                updateFeatureNumbers();
            } else {
                alert('At least one feature is required.');
            }
        }
    });

    function updateFeatureNumbers() {
        const features = document.querySelectorAll('.feature-item');
        features.forEach((feature, index) => {
            feature.querySelector('h6').textContent = `Feature ${index + 1}`;
        });
    }

    // Initialize icon pickers and color updates
    function initializeIconPickers() {
        // Icon picker functionality
        document.querySelectorAll('.icon-picker-btn:not([data-initialized])').forEach(btn => {
            btn.setAttribute('data-initialized', 'true');
            btn.addEventListener('click', function() {
                const input = this.closest('.input-group').querySelector('.icon-input');
                showIconPicker(input);
            });
        });

        // Color change updates
        document.querySelectorAll('.icon-color-input:not([data-initialized])').forEach(colorInput => {
            colorInput.setAttribute('data-initialized', 'true');
            colorInput.addEventListener('input', function() {
                console.log('Color changed to:', this.value);
                const preview = this.closest('.feature-item').querySelector('.icon-preview');
                if (preview) {
                    preview.style.color = this.value;
                    console.log('Preview updated');
                }
            });
        });

        // Icon class change updates
        document.querySelectorAll('.icon-input:not([data-initialized])').forEach(iconInput => {
            iconInput.setAttribute('data-initialized', 'true');
            iconInput.addEventListener('input', function() {
                const preview = this.closest('.feature-item').querySelector('.icon-preview');
                const colorInput = this.closest('.feature-item').querySelector('.icon-color-input');
                if (preview && colorInput) {
                    preview.className = this.value + ' icon-preview';
                    preview.style.color = colorInput.value;
                }
            });
        });
    }

    // Icon picker modal
    function showIconPicker(input) {
        const icons = [
            'fas fa-check', 'fas fa-star', 'fas fa-heart', 'fas fa-home', 'fas fa-user',
            'fas fa-users', 'fas fa-cog', 'fas fa-search', 'fas fa-envelope', 'fas fa-phone',
            'fas fa-map-marker-alt', 'fas fa-calendar', 'fas fa-clock', 'fas fa-edit', 'fas fa-trash',
            'fas fa-plus', 'fas fa-minus', 'fas fa-times', 'fas fa-arrow-right', 'fas fa-arrow-left',
            'fas fa-shield-alt', 'fas fa-handshake', 'fas fa-chart-line', 'fas fa-list-alt',
            'fas fa-building', 'fas fa-key', 'fas fa-award', 'fas fa-thumbs-up', 'fas fa-lightbulb',
            'fas fa-rocket', 'fas fa-target', 'fas fa-gem', 'fas fa-crown', 'fas fa-medal'
        ];

        let modalHtml = `
            <div class="modal fade" id="iconPickerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Choose an Icon</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
        `;

        icons.forEach(icon => {
            modalHtml += `
                <div class="col-2 text-center mb-3">
                    <button type="button" class="btn btn-outline-secondary icon-option" data-icon="${icon}">
                        <i class="${icon}"></i>
                    </button>
                </div>
            `;
        });

        modalHtml += `
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal
        const existingModal = document.getElementById('iconPickerModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal using Bootstrap 5
        const modal = new bootstrap.Modal(document.getElementById('iconPickerModal'));
        modal.show();

        // Handle icon selection
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', function() {
                const selectedIcon = this.getAttribute('data-icon');
                input.value = selectedIcon;

                // Update preview
                const preview = input.closest('.feature-item').querySelector('.icon-preview');
                const colorInput = input.closest('.feature-item').querySelector('.icon-color-input');
                if (preview) {
                    preview.className = selectedIcon + ' icon-preview';
                    preview.style.color = colorInput.value;
                }

                modal.hide();
            });
        });
    }

    // Initialize existing elements
    initializeIconPickers();
});
</script>
@endsection
