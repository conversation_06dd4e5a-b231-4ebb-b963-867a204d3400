@extends('admin.layouts.app')

@section('title', 'Edit Why Choose Us Section')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 class="admin-card-title">Edit Why Choose Us Section</h3>
                    <div class="admin-card-tools">
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to View
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ route('admin.why-choose-us.update') }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="admin-card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="form-group">
                                    <label for="title">Section Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ old('title', $whyChooseUs->title) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="subtitle">Section Subtitle</label>
                                    <input type="text" class="form-control" id="subtitle" name="subtitle" 
                                           value="{{ old('subtitle', $whyChooseUs->subtitle) }}">
                                </div>

                                <div class="form-group">
                                    <label for="content">Section Content</label>
                                    <textarea class="form-control ckeditor" id="content" name="content" rows="4">{{ old('content', $whyChooseUs->content) }}</textarea>
                                </div>

                                <!-- Features Section -->
                                <div class="features-section">
                                    <h5>Features</h5>
                                    <div id="features-container">
                                        @php
                                            $features = old('features', $whyChooseUs->additional_data['features'] ?? []);
                                        @endphp
                                        
                                        @foreach($features as $index => $feature)
                                            <div class="feature-item border p-3 mb-3" data-index="{{ $index }}">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6>Feature {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-danger remove-feature">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>FontAwesome Icon Class</label>
                                                            <input type="text" class="form-control" 
                                                                   name="features[{{ $index }}][icon]" 
                                                                   value="{{ $feature['icon'] ?? 'fas fa-check' }}"
                                                                   placeholder="e.g., fas fa-check">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Icon Color</label>
                                                            <input type="color" class="form-control" 
                                                                   name="features[{{ $index }}][icon_color]" 
                                                                   value="{{ $feature['icon_color'] ?? '#D4AF37' }}">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Title</label>
                                                    <input type="text" class="form-control" 
                                                           name="features[{{ $index }}][title]" 
                                                           value="{{ $feature['title'] ?? '' }}" required>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <label>Feature Description</label>
                                                    <textarea class="form-control" 
                                                              name="features[{{ $index }}][description]" 
                                                              rows="3" required>{{ $feature['description'] ?? '' }}</textarea>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <button type="button" class="btn btn-success" id="add-feature">
                                        <i class="fas fa-plus"></i> Add Feature
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Settings -->
                                <div class="form-group">
                                    <label for="background_color">Background Color</label>
                                    <input type="color" class="form-control" id="background_color" name="background_color" 
                                           value="{{ old('background_color', $whyChooseUs->background_color) }}">
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {{ old('is_active', $whyChooseUs->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Active Section</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Why Choose Us Section
                        </button>
                        <a href="{{ route('admin.why-choose-us.index') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor.create(document.querySelector('.ckeditor'))
        .catch(error => {
            console.error(error);
        });

    let featureIndex = {{ count($features) }};

    // Add new feature
    document.getElementById('add-feature').addEventListener('click', function() {
        const container = document.getElementById('features-container');
        const featureHtml = `
            <div class="feature-item border p-3 mb-3" data-index="${featureIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6>Feature ${featureIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-feature">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>FontAwesome Icon Class</label>
                            <input type="text" class="form-control" 
                                   name="features[${featureIndex}][icon]" 
                                   value="fas fa-check"
                                   placeholder="e.g., fas fa-check">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Icon Color</label>
                            <input type="color" class="form-control" 
                                   name="features[${featureIndex}][icon_color]" 
                                   value="#D4AF37">
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Feature Title</label>
                    <input type="text" class="form-control" 
                           name="features[${featureIndex}][title]" 
                           value="" required>
                </div>
                
                <div class="form-group">
                    <label>Feature Description</label>
                    <textarea class="form-control" 
                              name="features[${featureIndex}][description]" 
                              rows="3" required></textarea>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', featureHtml);
        featureIndex++;
        updateFeatureNumbers();
    });

    // Remove feature
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-feature')) {
            e.target.closest('.feature-item').remove();
            updateFeatureNumbers();
        }
    });

    function updateFeatureNumbers() {
        const features = document.querySelectorAll('.feature-item');
        features.forEach((feature, index) => {
            feature.querySelector('h6').textContent = `Feature ${index + 1}`;
        });
    }
});
</script>
@endsection
