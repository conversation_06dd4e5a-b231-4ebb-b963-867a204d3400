@extends('admin.layouts.app')

@section('title', 'Why Choose Us Section')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 class="admin-card-title">Why Choose Us Section</h3>
                    <div class="admin-card-tools">
                        <a href="{{ route('admin.why-choose-us.edit') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit Section
                        </a>
                        <form method="POST" action="{{ route('admin.why-choose-us.toggle-status') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-sm {{ $whyChooseUs->is_active ? 'btn-warning' : 'btn-success' }}">
                                <i class="fas fa-{{ $whyChooseUs->is_active ? 'eye-slash' : 'eye' }}"></i>
                                {{ $whyChooseUs->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                    </div>
                </div>

                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="section-preview">
                                <h4>{{ $whyChooseUs->title }}</h4>
                                @if($whyChooseUs->subtitle)
                                    <p class="text-muted">{{ $whyChooseUs->subtitle }}</p>
                                @endif
                                @if($whyChooseUs->content)
                                    <div class="content-preview">
                                        {!! $whyChooseUs->content !!}
                                    </div>
                                @endif

                                @if($whyChooseUs->additional_data && isset($whyChooseUs->additional_data['features']))
                                    <div class="features-preview mt-4">
                                        <h5>Features:</h5>
                                        <div class="row">
                                            @foreach($whyChooseUs->additional_data['features'] as $feature)
                                                <div class="col-md-6 mb-3">
                                                    <div class="feature-item p-3 border rounded">
                                                        <div class="d-flex align-items-start">
                                                            <div class="feature-icon me-3">
                                                                <i class="{{ $feature['icon'] }}" style="color: {{ $feature['icon_color'] }}; font-size: 2rem;"></i>
                                                            </div>
                                                            <div class="feature-content">
                                                                <h6 class="mb-2">{{ $feature['title'] }}</h6>
                                                                <p class="mb-0 text-muted small">{{ $feature['description'] }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="section-info">
                                <h5>Section Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge badge-{{ $whyChooseUs->is_active ? 'success' : 'secondary' }}">
                                                {{ $whyChooseUs->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Section Name:</strong></td>
                                        <td>{{ $whyChooseUs->section_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Sort Order:</strong></td>
                                        <td>{{ $whyChooseUs->sort_order }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $whyChooseUs->updated_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                    @if($whyChooseUs->background_color)
                                        <tr>
                                            <td><strong>Background:</strong></td>
                                            <td>
                                                <span class="color-preview" style="background-color: {{ $whyChooseUs->background_color }}; width: 20px; height: 20px; display: inline-block; border: 1px solid #ddd; border-radius: 3px;"></span>
                                                {{ $whyChooseUs->background_color }}
                                            </td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    .section-preview {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
    }
    
    .content-preview {
        margin: 1rem 0;
    }
    
    .feature-item {
        background: white;
        transition: all 0.3s ease;
    }
    
    .feature-item:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .feature-icon {
        flex-shrink: 0;
    }
    
    .section-info {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
    }
</style>
@endsection
