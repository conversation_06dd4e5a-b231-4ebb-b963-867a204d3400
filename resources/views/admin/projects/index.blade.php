@extends('admin.layouts.app')

@section('title', 'Projects')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Projects</span>
    </div>
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Projects Management</h1>
            <p class="page-subtitle">Manage your real estate projects and view them on the website</p>
        </div>
        <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Project
        </a>
    </div>
@endsection

@section('content')
    <div class="admin-card">
        <!-- Filters -->
        <div class="card-header">
            <form method="GET" action="{{ route('admin.projects.index') }}" class="row g-3" id="searchForm">
                <div class="col-md-3">
                    <input type="text" 
                           name="search" 
                           class="form-control" 
                           placeholder="Search projects..." 
                           value="{{ request('search') }}"
                           id="searchInput">
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="upcoming" {{ request('status') === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                        <option value="ongoing" {{ request('status') === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                        <option value="ready_to_move" {{ request('status') === 'ready_to_move' ? 'selected' : '' }}>Ready to Move</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="city" class="form-control">
                        <option value="">All Cities</option>
                        @foreach($cities as $city)
                            <option value="{{ $city }}" {{ request('city') === $city ? 'selected' : '' }}>
                                {{ $city }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="project_type" class="form-control">
                        <option value="">All Types</option>
                        @foreach($projectTypes as $type)
                            <option value="{{ $type }}" {{ request('project_type') === $type ? 'selected' : '' }}>
                                {{ $type }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn-admin btn-admin-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body">
            @if($projects->count() > 0)
                <!-- Projects Table -->
                <div class="table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Project</th>
                                <th>Location</th>
                                <th>Developer</th>
                                <th>Starting Price</th>
                                <th>Status</th>
                                <th>Featured</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($projects as $project)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>{{ $project->name }}</strong>
                                                @if($project->featured)
                                                    <span class="badge badge-warning ms-1">Featured</span>
                                                @endif
                                                <br>
                                                <small class="text-muted">{{ $project->project_type }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ $project->location }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $project->city }}, {{ $project->state }}</small>
                                    </td>
                                    <td>{{ $project->developer }}</td>
                                    <td>
                                        <strong class="text-primary">{{ $project->formatted_starting_price }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $project->price_range }}</small>
                                    </td>
                                    <td>
                                        @if($project->status === 'upcoming')
                                            <span class="badge badge-info">Upcoming</span>
                                        @elseif($project->status === 'ongoing')
                                            <span class="badge badge-warning">Ongoing</span>
                                        @elseif($project->status === 'ready_to_move')
                                            <span class="badge badge-success">Ready to Move</span>
                                        @else
                                            <span class="badge badge-secondary">Completed</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($project->featured)
                                            <i class="fas fa-star text-warning"></i>
                                        @else
                                            <i class="far fa-star text-muted"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ $project->project_url }}" 
                                               class="btn-admin btn-admin-outline btn-sm"
                                               title="View on Website"
                                               target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                            <a href="{{ route('admin.projects.show', $project) }}" 
                                               class="btn-admin btn-admin-outline btn-sm"
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.projects.edit', $project) }}" 
                                               class="btn-admin btn-admin-primary btn-sm"
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" 
                                                  action="{{ route('admin.projects.destroy', $project) }}" 
                                                  style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-admin btn-admin-danger btn-sm"
                                                        data-confirm-delete="Are you sure you want to delete this project?"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($projects->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $projects->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-5">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h5 class="empty-title">No Projects Found</h5>
                        <p class="empty-subtitle">
                            @if(request()->hasAny(['search', 'status', 'city', 'project_type']))
                                No projects match your current filters.
                            @else
                                Start building your project portfolio by adding your first project.
                            @endif
                        </p>
                        @if(request()->hasAny(['search', 'status', 'city', 'project_type']))
                            <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline me-2">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        @endif
                        <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary btn-admin-lg">
                            <i class="fas fa-plus me-2"></i>Create Your First Project
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .gap-1 {
        gap: 0.25rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .badge-info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--admin-info);
    }
</style>
@endpush
