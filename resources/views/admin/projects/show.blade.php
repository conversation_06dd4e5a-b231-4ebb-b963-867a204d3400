@extends('admin.layouts.app')

@section('title', 'View Project')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.projects.index') }}" class="breadcrumb-item">Projects</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">{{ $project->name }}</span>
    </div>
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">{{ $project->name }}</h1>
            <p class="page-subtitle">Project Details & Information</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ $project->project_url }}" class="btn-admin btn-admin-success" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>View on Website
            </a>
            <a href="{{ route('admin.projects.edit', $project) }}" class="btn-admin btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit Project
            </a>
            <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline">
                <i class="fas fa-arrow-left me-2"></i>Back to Projects
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <!-- Project Information -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        Project Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Project Name</h6>
                            <p class="mb-0"><strong>{{ $project->name }}</strong></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Developer</h6>
                            <p class="mb-0">{{ $project->developer }}</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Project Type</h6>
                            <p class="mb-0">{{ $project->project_type }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Property Types</h6>
                            <p class="mb-0">{{ $project->property_types }}</p>
                        </div>
                    </div>

                    @if($project->short_description)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Short Description</h6>
                            <p class="mb-0">{{ $project->short_description }}</p>
                        </div>
                    @endif

                    @if($project->description)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Full Description</h6>
                            <div class="description-content">
                                {!! nl2br(e($project->description)) !!}
                            </div>
                        </div>
                    @endif

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Starting Price</h6>
                            <p class="mb-0"><strong class="text-primary fs-5">{{ $project->formatted_starting_price }}</strong></p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Price Range</h6>
                            <p class="mb-0">{{ $project->price_range }}</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Possession Date</h6>
                            <p class="mb-0">{{ $project->possession_date }}</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Location</h6>
                            <p class="mb-0">{{ $project->location }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">City & State</h6>
                            <p class="mb-0">{{ $project->city }}, {{ $project->state }}</p>
                        </div>
                    </div>

                    @if($project->rera_id)
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-2">RERA ID</h6>
                                <p class="mb-0"><code>{{ $project->rera_id }}</code></p>
                            </div>
                            @if($project->total_area)
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Total Area</h6>
                                    <p class="mb-0">{{ $project->total_area }} sq ft</p>
                                </div>
                            @endif
                        </div>
                    @endif

                    @if($project->total_units)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Total Units</h6>
                            <p class="mb-0">{{ $project->total_units }} units</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- SEO Information -->
            @if($project->meta_title || $project->meta_description)
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2 text-info"></i>
                            SEO Information
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($project->meta_title)
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">Meta Title</h6>
                                <p class="mb-0">{{ $project->meta_title }}</p>
                            </div>
                        @endif

                        @if($project->meta_description)
                            <div class="mb-0">
                                <h6 class="text-muted mb-2">Meta Description</h6>
                                <p class="mb-0">{{ $project->meta_description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Status & Settings -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2 text-warning"></i>
                        Status & Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Project Status</h6>
                        @if($project->status === 'upcoming')
                            <span class="badge badge-info fs-6">Upcoming</span>
                        @elseif($project->status === 'ongoing')
                            <span class="badge badge-warning fs-6">Ongoing</span>
                        @elseif($project->status === 'ready_to_move')
                            <span class="badge badge-success fs-6">Ready to Move</span>
                        @else
                            <span class="badge badge-secondary fs-6">Completed</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Featured</h6>
                        @if($project->featured)
                            <span class="badge badge-warning fs-6">
                                <i class="fas fa-star me-1"></i>Featured
                            </span>
                        @else
                            <span class="text-muted">Not featured</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Active Status</h6>
                        @if($project->is_active)
                            <span class="badge badge-success fs-6">
                                <i class="fas fa-check me-1"></i>Active
                            </span>
                        @else
                            <span class="badge badge-danger fs-6">
                                <i class="fas fa-times me-1"></i>Inactive
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Project Details -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info me-2 text-primary"></i>
                        Project Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Project ID</small>
                        <div><strong>#{{ $project->id }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Slug</small>
                        <div><code>{{ $project->slug }}</code></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <div><strong>{{ $project->created_at->format('M d, Y H:i') }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <div><strong>{{ $project->updated_at->format('M d, Y H:i') }}</strong></div>
                    </div>
                    @if($project->deleted_at)
                        <div>
                            <small class="text-muted">Deleted</small>
                            <div><strong class="text-danger">{{ $project->deleted_at->format('M d, Y H:i') }}</strong></div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ $project->project_url }}" class="btn-admin btn-admin-success" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View on Website
                        </a>
                        
                        <a href="{{ route('admin.projects.edit', $project) }}" class="btn-admin btn-admin-primary">
                            <i class="fas fa-edit me-2"></i>Edit Project
                        </a>

                        <form method="POST" action="{{ route('admin.projects.destroy', $project) }}" style="display: inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="btn-admin btn-admin-danger w-100"
                                    data-confirm-delete="Are you sure you want to delete this project? This action cannot be undone.">
                                <i class="fas fa-trash me-2"></i>Delete Project
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .fs-5 {
        font-size: 1.25rem;
    }
    
    .fs-6 {
        font-size: 1rem;
    }
    
    code {
        background: #f8fafc;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
    }
    
    .description-content {
        background: #f8fafc;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid var(--admin-primary);
    }
    
    .d-grid {
        display: grid;
    }
    
    .gap-2 {
        gap: 0.5rem;
    }
    
    .badge-info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--admin-info);
    }
</style>
@endpush
