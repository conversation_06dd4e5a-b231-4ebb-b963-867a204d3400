@extends('admin.layouts.app')

@section('title', 'Edit Profile')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.profile.show') }}" class="breadcrumb-item">Profile</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Edit</span>
    </div>
    <h1 class="page-title">Edit Profile</h1>
    <p class="page-subtitle">Update your account information and preferences</p>
    <div class="page-actions">
        <a href="{{ route('admin.profile.show') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-4">
            <!-- Avatar Section -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2 text-primary"></i>
                        Profile Picture
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="avatar-upload-container">
                        <div class="current-avatar mb-3">
                            <img src="{{ $admin->getAvatarUrl() }}" 
                                 alt="{{ $admin->name }}" 
                                 class="profile-avatar-large"
                                 id="avatarPreview">
                        </div>
                        
                        <div class="avatar-actions">
                            <label for="avatar" class="btn-admin btn-admin-primary btn-admin-sm">
                                <i class="fas fa-upload me-2"></i>Upload New
                            </label>
                            @if($admin->avatar)
                                <form action="{{ route('admin.profile.remove-avatar') }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn-admin btn-admin-outline btn-admin-sm"
                                            onclick="return confirm('Are you sure you want to remove your avatar?')">
                                        <i class="fas fa-trash me-2"></i>Remove
                                    </button>
                                </form>
                            @endif
                        </div>
                        
                        <p class="text-muted mt-2 mb-0">
                            <small>Recommended: 200x200px, max 2MB<br>
                            Formats: JPG, PNG, GIF</small>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Account Info -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2 text-success"></i>
                        Account Security
                    </h5>
                </div>
                <div class="card-body">
                    <div class="security-info">
                        <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-key text-warning"></i>
                            </div>
                            <div class="security-content">
                                <div class="security-title">Password</div>
                                <div class="security-desc">Last changed {{ $admin->updated_at->diffForHumans() }}</div>
                            </div>
                            <a href="{{ route('admin.profile.change-password') }}" class="btn-admin btn-admin-outline btn-admin-sm">
                                Change
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Profile Form -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit me-2 text-primary"></i>
                        Personal Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Hidden file input for avatar -->
                        <input type="file" id="avatar" name="avatar" accept="image/*" style="display: none;">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label required">Full Name</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $admin->name) }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label required">Email Address</label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', $admin->email) }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" 
                                           class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" 
                                           name="phone" 
                                           value="{{ old('phone', $admin->phone) }}" 
                                           placeholder="+****************">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" 
                                           class="form-control @error('department') is-invalid @enderror" 
                                           id="department" 
                                           name="department" 
                                           value="{{ old('department', $admin->department) }}" 
                                           placeholder="e.g., Sales, Marketing, IT">
                                    @error('department')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control @error('bio') is-invalid @enderror" 
                                      id="bio" 
                                      name="bio" 
                                      rows="4" 
                                      placeholder="Tell us a bit about yourself...">{{ old('bio', $admin->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Maximum 1000 characters</div>
                        </div>

                        <!-- Read-only fields -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Role</label>
                                    <input type="text" class="form-control" value="{{ $admin->role_name }}" readonly>
                                    <div class="form-text">Contact a super admin to change your role</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Status</label>
                                    <input type="text" class="form-control" value="{{ ucfirst($admin->status) }}" readonly>
                                    <div class="form-text">Account status is managed by administrators</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                            <a href="{{ route('admin.profile.show') }}" class="btn-admin btn-admin-outline">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar preview functionality
    const avatarInput = document.getElementById('avatar');
    const avatarPreview = document.getElementById('avatarPreview');

    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                avatarPreview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Character counter for bio
    const bioTextarea = document.getElementById('bio');
    const maxLength = 1000;
    
    if (bioTextarea) {
        const counterDiv = document.createElement('div');
        counterDiv.className = 'form-text text-end';
        bioTextarea.parentNode.appendChild(counterDiv);
        
        function updateCounter() {
            const remaining = maxLength - bioTextarea.value.length;
            counterDiv.textContent = `${remaining} characters remaining`;
            counterDiv.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end';
        }
        
        bioTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
@endpush

@push('styles')
<style>
.avatar-upload-container {
    position: relative;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.avatar-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.security-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.security-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    border: 2px solid #e9ecef;
}

.security-content {
    flex: 1;
}

.security-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.security-desc {
    font-size: 0.85rem;
    color: #6c757d;
}

.form-label.required::after {
    content: ' *';
    color: #dc3545;
}

.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .avatar-actions {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .security-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .security-icon {
        margin-right: 0;
    }
}
</style>
@endpush
