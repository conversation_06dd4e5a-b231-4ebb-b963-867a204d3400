@extends('admin.layouts.app')

@section('title', 'Change Password')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.profile.show') }}" class="breadcrumb-item">Profile</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Change Password</span>
    </div>
    <h1 class="page-title">Change Password</h1>
    <p class="page-subtitle">Update your account password for better security</p>
    <div class="page-actions">
        <a href="{{ route('admin.profile.show') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>
    </div>
@endsection

@section('content')
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Password Change Form -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2 text-warning"></i>
                        Update Password
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.profile.change-password.update') }}" method="POST" id="passwordForm">
                        @csrf
                        @method('PUT')

                        <div class="form-group mb-3">
                            <label for="current_password" class="form-label required">Current Password</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('current_password') is-invalid @enderror" 
                                       id="current_password" 
                                       name="current_password" 
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye" id="current_password_icon"></i>
                                </button>
                            </div>
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="password" class="form-label required">New Password</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       required
                                       minlength="8">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password_icon"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Password must be at least 8 characters long
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label for="password_confirmation" class="form-label required">Confirm New Password</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       required
                                       minlength="8">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                    <i class="fas fa-eye" id="password_confirmation_icon"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Re-enter your new password to confirm
                            </div>
                        </div>

                        <!-- Password Strength Indicator -->
                        <div class="password-strength mb-4" id="passwordStrength" style="display: none;">
                            <div class="strength-label">Password Strength:</div>
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <div class="strength-text" id="strengthText"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Update Password
                            </button>
                            <a href="{{ route('admin.profile.show') }}" class="btn-admin btn-admin-outline">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2 text-success"></i>
                        Security Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="security-tips">
                        <div class="tip-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Use a combination of uppercase and lowercase letters
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Include numbers and special characters
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Make it at least 8 characters long
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Avoid using personal information
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Don't reuse passwords from other accounts
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmField = document.getElementById('password_confirmation');
    const strengthIndicator = document.getElementById('passwordStrength');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    const form = document.getElementById('passwordForm');

    // Password strength checker
    function checkPasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) score += 1;
        else feedback.push('At least 8 characters');

        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('Lowercase letter');

        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('Uppercase letter');

        if (/[0-9]/.test(password)) score += 1;
        else feedback.push('Number');

        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        else feedback.push('Special character');

        return { score, feedback };
    }

    function updateStrengthIndicator(password) {
        if (password.length === 0) {
            strengthIndicator.style.display = 'none';
            return;
        }

        strengthIndicator.style.display = 'block';
        const { score, feedback } = checkPasswordStrength(password);
        
        const percentage = (score / 5) * 100;
        strengthFill.style.width = percentage + '%';

        let strengthClass = '';
        let strengthLabel = '';

        if (score <= 2) {
            strengthClass = 'weak';
            strengthLabel = 'Weak';
        } else if (score <= 3) {
            strengthClass = 'fair';
            strengthLabel = 'Fair';
        } else if (score <= 4) {
            strengthClass = 'good';
            strengthLabel = 'Good';
        } else {
            strengthClass = 'strong';
            strengthLabel = 'Strong';
        }

        strengthFill.className = 'strength-fill ' + strengthClass;
        strengthText.textContent = strengthLabel;
        
        if (feedback.length > 0) {
            strengthText.textContent += ' (Missing: ' + feedback.join(', ') + ')';
        }
    }

    // Real-time password strength checking
    passwordField.addEventListener('input', function() {
        updateStrengthIndicator(this.value);
        validatePasswordMatch();
    });

    // Password confirmation validation
    function validatePasswordMatch() {
        const password = passwordField.value;
        const confirm = confirmField.value;
        
        if (confirm.length > 0) {
            if (password === confirm) {
                confirmField.classList.remove('is-invalid');
                confirmField.classList.add('is-valid');
            } else {
                confirmField.classList.remove('is-valid');
                confirmField.classList.add('is-invalid');
            }
        } else {
            confirmField.classList.remove('is-valid', 'is-invalid');
        }
    }

    confirmField.addEventListener('input', validatePasswordMatch);

    // Form submission validation
    form.addEventListener('submit', function(e) {
        const password = passwordField.value;
        const confirm = confirmField.value;
        
        if (password !== confirm) {
            e.preventDefault();
            confirmField.classList.add('is-invalid');
            alert('Passwords do not match!');
            return false;
        }

        const { score } = checkPasswordStrength(password);
        if (score < 3) {
            if (!confirm('Your password is weak. Are you sure you want to continue?')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.form-label.required::after {
    content: ' *';
    color: #dc3545;
}

.password-strength {
    margin-bottom: 1rem;
}

.strength-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.strength-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.strength-fill.weak {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.strength-fill.fair {
    background: linear-gradient(90deg, #fd7e14, #e55a00);
}

.strength-fill.good {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.strength-fill.strong {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.strength-text {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.security-tips {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.tip-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
}

.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

.input-group .btn {
    border-left: 0;
}

.input-group .form-control:focus + .btn {
    border-color: #cfaa13;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}
</style>
@endpush
