@extends('admin.layouts.app')

@section('title', 'My Profile')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Profile</span>
    </div>
    <h1 class="page-title">My Profile</h1>
    <p class="page-subtitle">Manage your account information and settings</p>
    <div class="page-actions">
        <a href="{{ route('admin.profile.edit') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit Profile
        </a>
        <a href="{{ route('admin.profile.change-password') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-key me-2"></i>Change Password
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="profile-avatar-container mb-4">
                        <img src="{{ $admin->getAvatarUrl() }}" 
                             alt="{{ $admin->name }}" 
                             class="profile-avatar-large">
                        <div class="profile-status-badge">
                            <i class="fas fa-circle text-success"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">{{ $admin->name }}</h4>
                    <p class="text-muted mb-2">{{ $admin->role_name }}</p>
                    @if($admin->department)
                        <p class="text-muted mb-3">{{ $admin->department }}</p>
                    @endif
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <span class="stat-value">{{ $admin->created_at->format('M Y') }}</span>
                            <span class="stat-label">Member Since</span>
                        </div>
                        @if($admin->last_login_at)
                            <div class="stat-item">
                                <span class="stat-value">{{ $admin->last_login_at->diffForHumans() }}</span>
                                <span class="stat-label">Last Login</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="{{ route('admin.profile.edit') }}" class="quick-action-item">
                            <i class="fas fa-user-edit"></i>
                            <span>Edit Profile</span>
                        </a>
                        <a href="{{ route('admin.profile.change-password') }}" class="quick-action-item">
                            <i class="fas fa-key"></i>
                            <span>Change Password</span>
                        </a>
                        <a href="{{ route('admin.dashboard') }}" class="quick-action-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Profile Information -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        Profile Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="profile-info-grid">
                        <div class="info-item">
                            <label class="info-label">Full Name</label>
                            <div class="info-value">{{ $admin->name }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Email Address</label>
                            <div class="info-value">{{ $admin->email }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Phone Number</label>
                            <div class="info-value">{{ $admin->phone ?: 'Not provided' }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Department</label>
                            <div class="info-value">{{ $admin->department ?: 'Not specified' }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Role</label>
                            <div class="info-value">
                                <span class="badge badge-role badge-{{ $admin->role }}">
                                    {{ $admin->role_name }}
                                </span>
                            </div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Status</label>
                            <div class="info-value">
                                <span class="badge badge-status badge-{{ $admin->status }}">
                                    <i class="fas fa-circle me-1"></i>
                                    {{ ucfirst($admin->status) }}
                                </span>
                            </div>
                        </div>

                        @if($admin->bio)
                            <div class="info-item full-width">
                                <label class="info-label">Bio</label>
                                <div class="info-value">{{ $admin->bio }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Account Activity -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-info"></i>
                        Account Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus text-success"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Account Created</div>
                                <div class="activity-time">{{ $admin->created_at->format('M d, Y \a\t g:i A') }}</div>
                            </div>
                        </div>

                        @if($admin->last_login_at)
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-sign-in-alt text-primary"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Last Login</div>
                                    <div class="activity-time">{{ $admin->last_login_at->format('M d, Y \a\t g:i A') }}</div>
                                </div>
                            </div>
                        @endif

                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-edit text-warning"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Profile Updated</div>
                                <div class="activity-time">{{ $admin->updated_at->format('M d, Y \a\t g:i A') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
.profile-avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.profile-status-badge {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: #fff;
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-weight: 600;
    color: #cfaa13;
    font-size: 0.9rem;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.quick-action-item:hover {
    background: #f8f9fa;
    color: #cfaa13;
    text-decoration: none;
    transform: translateX(5px);
}

.quick-action-item i {
    width: 20px;
    margin-right: 0.75rem;
}

.profile-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    display: block;
}

.info-value {
    color: #6c757d;
    font-size: 0.95rem;
}

.badge-role {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.badge-super_admin {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.badge-admin {
    background: linear-gradient(135deg, #cfaa13, #b8941a);
    color: white;
}

.badge-editor {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.badge-status {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.badge-active {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.badge-inactive {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    width: 2px;
    height: 20px;
    background: #e9ecef;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    border: 2px solid #e9ecef;
}

.activity-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 0.85rem;
    color: #6c757d;
}
</style>
@endpush
