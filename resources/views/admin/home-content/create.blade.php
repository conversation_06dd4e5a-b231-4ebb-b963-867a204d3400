@extends('admin.layouts.app')

@section('title', 'Create Home Content')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.home-content.index') }}" class="breadcrumb-item">Home Content</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Create</span>
    </div>
    <h1 class="page-title">Create Home Content Section</h1>
    <p class="page-subtitle">Add a new content section to your homepage</p>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2 text-primary"></i>
                        New Content Section
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.home-content.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="section_name" class="form-label">Section Type *</label>
                                    <select class="form-select @error('section_name') is-invalid @enderror"
                                            id="section_name"
                                            name="section_name"
                                            required>
                                        <option value="">Select Section Type</option>
                                        @foreach(\App\Models\HomeContent::getSectionTypes() as $key => $label)
                                            <option value="{{ $key }}" {{ old('section_name') == $key ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('section_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', 0) }}">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" 
                                   class="form-control @error('title') is-invalid @enderror" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title') }}" 
                                   required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   class="form-control @error('subtitle') is-invalid @enderror" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   value="{{ old('subtitle') }}">
                            @error('subtitle')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="content" class="form-label">Content</label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" 
                                      name="content" 
                                      rows="5">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="images" class="form-label">Images</label>
                            <input type="file" 
                                   class="form-control @error('images.*') is-invalid @enderror" 
                                   id="images" 
                                   name="images[]" 
                                   multiple 
                                   accept="image/*">
                            <small class="form-text text-muted">You can select multiple images. Supported formats: JPEG, PNG, JPG, GIF (Max: 2MB each)</small>
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           class="form-control @error('button_text') is-invalid @enderror" 
                                           id="button_text" 
                                           name="button_text" 
                                           value="{{ old('button_text') }}">
                                    @error('button_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="button_url" class="form-label">Button URL</label>
                                    <input type="url" 
                                           class="form-control @error('button_url') is-invalid @enderror" 
                                           id="button_url" 
                                           name="button_url" 
                                           value="{{ old('button_url') }}">
                                    @error('button_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Create Content Section
                            </button>
                            <a href="{{ route('admin.home-content.index') }}" class="btn-admin btn-admin-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Content Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-content">
                        <h6>Section Name</h6>
                        <p>Use descriptive names like "hero_section", "about_preview", "services_overview"</p>
                        
                        <h6>Sort Order</h6>
                        <p>Lower numbers appear first. Use increments of 10 (10, 20, 30) for easy reordering.</p>
                        
                        <h6>Images</h6>
                        <p>Upload high-quality images. Multiple images can be used for galleries or carousels.</p>
                        
                        <h6>Button</h6>
                        <p>Add call-to-action buttons to guide users to important pages.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
