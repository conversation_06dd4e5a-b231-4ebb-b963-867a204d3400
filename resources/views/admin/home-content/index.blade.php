@extends('admin.layouts.app')

@section('title', 'Home Content')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Home Content</span>
    </div>
    <h1 class="page-title">Home Content Management</h1>
    <p class="page-subtitle">Manage sections and content displayed on the homepage</p>
    <div class="page-actions">
        <a href="{{ route('admin.home-content.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Section
        </a>
    </div>
@endsection

@section('content')
    <div class="admin-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-home me-2 text-primary"></i>
                Home Content Sections
            </h5>
        </div>
        <div class="card-body">
            @if($contents->count() > 0)
                <div class="table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Section</th>
                                <th>Title</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($contents as $content)
                                <tr>
                                    <td>
                                        @if($content->first_image_url)
                                            <img src="{{ $content->first_image_url }}" 
                                                 alt="{{ $content->title }}" 
                                                 class="table-image">
                                        @else
                                            <div class="table-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $content->section_type_label }}</span>
                                    </td>
                                    <td>
                                        <div class="table-title">{{ $content->title }}</div>
                                        @if($content->subtitle)
                                            <div class="table-subtitle">{{ $content->subtitle }}</div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-outline">{{ $content->sort_order }}</span>
                                    </td>
                                    <td>
                                        @if($content->is_active)
                                            <span class="status-badge active">
                                                <i class="fas fa-check-circle"></i>
                                                Active
                                            </span>
                                        @else
                                            <span class="status-badge inactive">
                                                <i class="fas fa-times-circle"></i>
                                                Inactive
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.home-content.edit', $content) }}" 
                                               class="btn-admin btn-admin-primary btn-admin-sm" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.home-content.destroy', $content) }}" 
                                                  method="POST" 
                                                  class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to delete this content section?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-admin btn-admin-danger btn-admin-sm" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h4 class="empty-title">No Home Content Found</h4>
                    <p class="empty-subtitle">Start by creating your first home content section to organize your homepage.</p>
                    <a href="{{ route('admin.home-content.create') }}" class="btn-admin btn-admin-primary">
                        <i class="fas fa-plus me-2"></i>Create First Section
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
