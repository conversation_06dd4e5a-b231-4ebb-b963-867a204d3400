@extends('admin.layouts.app')

@section('title', 'Edit Setting')

@section('content')
<div class="admin-header">
    <div class="admin-header-content">
        <h1 class="admin-title">
            <i class="fas fa-edit me-2"></i>
            Edit Setting
        </h1>
        <div class="admin-actions">
            <a href="{{ route('admin.settings.index', ['category' => $setting->category]) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Settings
            </a>
        </div>
    </div>
</div>

<div class="admin-content">
    <form method="POST" action="{{ route('admin.settings.update-setting', $setting) }}">
        @csrf
        @method('PUT')
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="key" class="form-label">Setting Key <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('key') is-invalid @enderror" 
                                           id="key" name="key" value="{{ old('key', $setting->key) }}" required>
                                    <div class="form-text">Unique identifier for this setting</div>
                                    @error('key')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="label" class="form-label">Display Label <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('label') is-invalid @enderror" 
                                           id="label" name="label" value="{{ old('label', $setting->label) }}" required>
                                    <div class="form-text">Human-readable label for this setting</div>
                                    @error('label')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Setting Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" 
                                            id="type" name="type" required>
                                        <option value="">Select Type</option>
                                        @foreach($types as $typeKey => $typeLabel)
                                            <option value="{{ $typeKey }}" {{ old('type', $setting->type) === $typeKey ? 'selected' : '' }}>
                                                {{ $typeLabel }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category') is-invalid @enderror" 
                                            id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories as $catKey => $catLabel)
                                            <option value="{{ $catKey }}" {{ old('category', $setting->category) === $catKey ? 'selected' : '' }}>
                                                {{ $catLabel }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $setting->description) }}</textarea>
                            <div class="form-text">Help text that will be shown below the setting field</div>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="options-field" style="display: {{ $setting->type === 'select' ? 'block' : 'none' }};">
                            <label for="options" class="form-label">Options</label>
                            <textarea class="form-control @error('options') is-invalid @enderror" 
                                      id="options" name="options" rows="5">{{ old('options', $optionsString) }}</textarea>
                            <div class="form-text">
                                For select type only. Enter one option per line.<br>
                                Format: <code>value=Display Text</code> or just <code>value</code><br>
                                Example:<br>
                                <code>yes=Yes</code><br>
                                <code>no=No</code>
                            </div>
                            @error('options')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Current Value -->
                @if($setting->value)
                    <div class="admin-card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Current Value</h5>
                        </div>
                        <div class="card-body">
                            @if($setting->type === 'file' && $setting->value)
                                @if(in_array(strtolower(pathinfo($setting->value, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']))
                                    <img src="{{ asset('storage/' . $setting->value) }}" 
                                         alt="{{ $setting->label }}" 
                                         class="img-fluid rounded">
                                @else
                                    <a href="{{ asset('storage/' . $setting->value) }}" 
                                       target="_blank" class="btn btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>
                                        Download File
                                    </a>
                                @endif
                            @elseif($setting->type === 'boolean')
                                <span class="badge bg-{{ $setting->value ? 'success' : 'secondary' }}">
                                    {{ $setting->value ? 'Yes' : 'No' }}
                                </span>
                            @elseif($setting->type === 'color')
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 30px; height: 30px; background-color: {{ $setting->value }}; border: 1px solid #ddd; border-radius: 4px;"></div>
                                    <code>{{ $setting->value }}</code>
                                </div>
                            @else
                                <div class="text-break">{{ $setting->formatted_value }}</div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Settings -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $setting->sort_order) }}" min="0">
                            <div class="form-text">Order in which this setting appears</div>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_required" 
                                       id="is_required" value="1" {{ old('is_required', $setting->is_required) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_required">
                                    Required Field
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" 
                                       id="is_active" value="1" {{ old('is_active', $setting->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="admin-card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-save me-1"></i>
                            Update Setting
                        </button>
                        <a href="{{ route('admin.settings.index', ['category' => $setting->category]) }}" class="btn btn-outline-secondary w-100">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const optionsField = document.getElementById('options-field');
    
    function toggleOptionsField() {
        if (typeSelect.value === 'select') {
            optionsField.style.display = 'block';
        } else {
            optionsField.style.display = 'none';
        }
    }
    
    typeSelect.addEventListener('change', toggleOptionsField);
    toggleOptionsField(); // Initial check
});
</script>
@endsection
