@extends('admin.layouts.app')

@section('title', 'Settings')

@section('content')
<div class="admin-header">
    <div class="admin-header-content">
        <h1 class="admin-title">
            <i class="fas fa-cog me-2"></i>
            Settings Management
        </h1>
        <div class="admin-actions">
            <a href="{{ route('admin.settings.create') }}" class="btn btn-outline-primary">
                <i class="fas fa-plus me-1"></i>
                Add Setting
            </a>
            <form action="{{ route('admin.settings.clear-cache') }}" method="POST" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-warning">
                    <i class="fas fa-sync me-1"></i>
                    Clear Cache
                </button>
            </form>
        </div>
    </div>
</div>

<div class="admin-content">
    <!-- Category Tabs -->
    <div class="admin-card mb-4">
        <div class="card-body">
            <ul class="nav nav-pills nav-fill">
                @foreach($categories as $key => $label)
                    <li class="nav-item">
                        <a class="nav-link {{ $category === $key ? 'active' : '' }}" 
                           href="{{ route('admin.settings.index', ['category' => $key]) }}">
                            <i class="fas fa-{{ $key === 'general' ? 'cog' : ($key === 'company' ? 'building' : ($key === 'contact' ? 'phone' : ($key === 'social' ? 'share-alt' : ($key === 'seo' ? 'search' : ($key === 'email' ? 'envelope' : ($key === 'appearance' ? 'palette' : 'puzzle-piece')))))) }} me-2"></i>
                            {{ $label }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>

    <!-- Settings Form -->
    @if($settings->count() > 0)
        <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <input type="hidden" name="category" value="{{ $category }}">
            
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-{{ $category === 'general' ? 'cog' : ($category === 'company' ? 'building' : ($category === 'contact' ? 'phone' : ($category === 'social' ? 'share-alt' : ($category === 'seo' ? 'search' : ($category === 'email' ? 'envelope' : ($category === 'appearance' ? 'palette' : 'puzzle-piece')))))) }} me-2"></i>
                        {{ $categories[$category] }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($settings as $setting)
                            <div class="col-md-6 mb-4">
                                <label for="{{ $setting->key }}" class="form-label">
                                    {{ $setting->label }}
                                    @if($setting->is_required)
                                        <span class="text-danger">*</span>
                                    @endif
                                </label>

                                @if($setting->type === 'text')
                                    <input type="text" 
                                           class="form-control @error($setting->key) is-invalid @enderror" 
                                           id="{{ $setting->key }}" 
                                           name="{{ $setting->key }}" 
                                           value="{{ old($setting->key, $setting->value) }}"
                                           {{ $setting->is_required ? 'required' : '' }}>

                                @elseif($setting->type === 'textarea')
                                    <textarea class="form-control @error($setting->key) is-invalid @enderror" 
                                              id="{{ $setting->key }}" 
                                              name="{{ $setting->key }}" 
                                              rows="4"
                                              {{ $setting->is_required ? 'required' : '' }}>{{ old($setting->key, $setting->value) }}</textarea>

                                @elseif($setting->type === 'number')
                                    <input type="number" 
                                           class="form-control @error($setting->key) is-invalid @enderror" 
                                           id="{{ $setting->key }}" 
                                           name="{{ $setting->key }}" 
                                           value="{{ old($setting->key, $setting->value) }}"
                                           {{ $setting->is_required ? 'required' : '' }}>

                                @elseif($setting->type === 'email')
                                    <input type="email" 
                                           class="form-control @error($setting->key) is-invalid @enderror" 
                                           id="{{ $setting->key }}" 
                                           name="{{ $setting->key }}" 
                                           value="{{ old($setting->key, $setting->value) }}"
                                           {{ $setting->is_required ? 'required' : '' }}>

                                @elseif($setting->type === 'url')
                                    <input type="url" 
                                           class="form-control @error($setting->key) is-invalid @enderror" 
                                           id="{{ $setting->key }}" 
                                           name="{{ $setting->key }}" 
                                           value="{{ old($setting->key, $setting->value) }}"
                                           {{ $setting->is_required ? 'required' : '' }}>

                                @elseif($setting->type === 'color')
                                    <input type="color" 
                                           class="form-control form-control-color @error($setting->key) is-invalid @enderror" 
                                           id="{{ $setting->key }}" 
                                           name="{{ $setting->key }}" 
                                           value="{{ old($setting->key, $setting->value ?: '#000000') }}"
                                           {{ $setting->is_required ? 'required' : '' }}>

                                @elseif($setting->type === 'select')
                                    <select class="form-select @error($setting->key) is-invalid @enderror" 
                                            id="{{ $setting->key }}" 
                                            name="{{ $setting->key }}"
                                            {{ $setting->is_required ? 'required' : '' }}>
                                        @if(!$setting->is_required)
                                            <option value="">Select an option</option>
                                        @endif
                                        @if($setting->options)
                                            @foreach($setting->options as $optionKey => $optionValue)
                                                <option value="{{ $optionKey }}" 
                                                        {{ old($setting->key, $setting->value) == $optionKey ? 'selected' : '' }}>
                                                    {{ $optionValue }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>

                                @elseif($setting->type === 'boolean')
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ $setting->key }}" 
                                               name="{{ $setting->key }}" 
                                               value="1"
                                               {{ old($setting->key, $setting->value) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="{{ $setting->key }}">
                                            Enable
                                        </label>
                                    </div>

                                @elseif($setting->type === 'file')
                                    @if($setting->value)
                                        <div class="mb-2">
                                            <small class="text-muted">Current file:</small>
                                            <div>
                                                @if(in_array(strtolower(pathinfo($setting->value, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']))
                                                    <img src="{{ asset('storage/' . $setting->value) }}"
                                                         alt="{{ $setting->label }}"
                                                         class="img-thumbnail"
                                                         style="max-height: 100px;">
                                                @else
                                                    <a href="{{ asset('storage/' . $setting->value) }}"
                                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-download me-1"></i>
                                                        Download Current File
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                    <input type="file"
                                           class="form-control @error($setting->key) is-invalid @enderror"
                                           id="{{ $setting->key }}"
                                           name="{{ $setting->key }}"
                                           {{ $setting->is_required && !$setting->value ? 'required' : '' }}>
                                @endif

                                @if($setting->description)
                                    <div class="form-text">{{ $setting->description }}</div>
                                @endif

                                @error($setting->key)
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                <!-- Setting Actions -->
                                <div class="mt-2">
                                    <a href="{{ route('admin.settings.edit', $setting) }}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.settings.destroy', $setting) }}" 
                                          method="POST" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this setting?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    @else
        <div class="admin-card">
            <div class="card-body text-center py-5">
                <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No settings found for this category</h5>
                <p class="text-muted">Start by adding your first setting for the {{ $categories[$category] }} category.</p>
                <a href="{{ route('admin.settings.create') }}?category={{ $category }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add Setting
                </a>
            </div>
        </div>
    @endif
</div>
@endsection
