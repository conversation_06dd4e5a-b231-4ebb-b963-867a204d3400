@extends('admin.layouts.app')

@section('title', 'Edit Hero Section')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.hero-section.index') }}" class="breadcrumb-item">Hero Section</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Edit</span>
    </div>
    <h1 class="page-title">Edit Hero Section</h1>
    <p class="page-subtitle">Update the main hero section content and settings</p>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        Edit Hero Section
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.hero-section.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title', $heroSection->title) }}" 
                                           placeholder="Enter hero section title" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="subtitle" class="form-label">Subtitle</label>
                                    <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                           id="subtitle" name="subtitle" value="{{ old('subtitle', $heroSection->subtitle) }}" 
                                           placeholder="Enter hero section subtitle">
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="content" class="form-label">Content</label>
                            <textarea class="form-control ckeditor @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="6" 
                                      placeholder="Enter hero section content">{{ old('content', $heroSection->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                           id="button_text" name="button_text" value="{{ old('button_text', $heroSection->button_text) }}" 
                                           placeholder="Enter button text">
                                    @error('button_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="button_url" class="form-label">Button URL</label>
                                    <input type="text" class="form-control @error('button_url') is-invalid @enderror" 
                                           id="button_url" name="button_url" value="{{ old('button_url', $heroSection->button_url) }}" 
                                           placeholder="Enter button URL">
                                    @error('button_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Background Type</label>
                            <div class="form-check-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="background_type" id="bg_color" 
                                           value="color" {{ old('background_type', $heroSection->background_type) == 'color' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="bg_color">Color Background</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="background_type" id="bg_image" 
                                           value="image" {{ old('background_type', $heroSection->background_type) == 'image' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="bg_image">Image Background</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="color_picker_group" style="display: none;">
                            <label for="background_color" class="form-label">Background Color</label>
                            <input type="color" class="form-control form-control-color @error('background_color') is-invalid @enderror" 
                                   id="background_color" name="background_color" value="{{ old('background_color', $heroSection->background_color ?? '#f8f9fa') }}">
                            @error('background_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group" id="background_image_group" style="display: none;">
                            <label for="background_image" class="form-label">Background Image</label>
                            @if($heroSection->background_image)
                                <div class="current-image mb-3">
                                    <img src="{{ asset('storage/' . $heroSection->background_image) }}" alt="Current background" class="img-thumbnail" style="max-height: 200px;">
                                    <p class="text-muted mt-2">Current background image</p>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('background_image') is-invalid @enderror" 
                                   id="background_image" name="background_image" accept="image/*">
                            @error('background_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        @if($heroSection->images && count($heroSection->images) > 0)
                            <div class="form-group">
                                <label class="form-label">Current Additional Images</label>
                                <div class="current-images">
                                    @foreach($heroSection->image_urls as $imageUrl)
                                        <div class="current-image">
                                            <img src="{{ $imageUrl }}" alt="Current image" class="img-thumbnail">
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <div class="form-group">
                            <label for="images" class="form-label">Additional Images</label>
                            <input type="file" class="form-control @error('images.*') is-invalid @enderror" 
                                   id="images" name="images[]" accept="image/*" multiple>
                            <small class="form-text text-muted">You can select multiple images. This will replace existing images.</small>
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ old('is_active', $heroSection->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active (Display on homepage)
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Update Hero Section
                            </button>
                            <a href="{{ route('admin.hero-section.index') }}" class="btn-admin btn-admin-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Hero Section Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-content">
                        <h6>Title</h6>
                        <p>Keep it concise and impactful. This is the first thing visitors see.</p>
                        
                        <h6>Subtitle</h6>
                        <p>Provide additional context or your value proposition.</p>
                        
                        <h6>Content</h6>
                        <p>Brief description that encourages visitors to take action.</p>
                        
                        <h6>Button</h6>
                        <p>Use action-oriented text like "Get Started", "Learn More", "Contact Us".</p>
                        
                        <h6>Background</h6>
                        <p>Choose between a solid color or an image background. Images should be high-quality and not too busy.</p>
                        
                        <h6>Additional Images</h6>
                        <p>Optional images that can be used in carousels or galleries within the hero section.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
.current-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.current-image {
    position: relative;
}

.current-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.form-check-group {
    display: flex;
    gap: 20px;
}

.help-content h6 {
    color: var(--admin-primary);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.help-content h6:first-child {
    margin-top: 0;
}

.help-content p {
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorRadio = document.getElementById('bg_color');
    const imageRadio = document.getElementById('bg_image');
    const colorGroup = document.getElementById('color_picker_group');
    const imageGroup = document.getElementById('background_image_group');

    function toggleBackgroundType() {
        if (colorRadio.checked) {
            colorGroup.style.display = 'block';
            imageGroup.style.display = 'none';
        } else if (imageRadio.checked) {
            colorGroup.style.display = 'none';
            imageGroup.style.display = 'block';
        }
    }

    colorRadio.addEventListener('change', toggleBackgroundType);
    imageRadio.addEventListener('change', toggleBackgroundType);

    // Initialize on page load
    toggleBackgroundType();
});
</script>
@endpush
