@extends('admin.layouts.app')

@section('title', 'Hero Section Management')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Hero Section</span>
    </div>
    <h1 class="page-title">Hero Section Management</h1>
    <p class="page-subtitle">Manage the main hero section displayed on the homepage</p>
    <div class="page-actions">
        <a href="{{ route('admin.hero-section.edit') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit Hero Section
        </a>
        <form method="POST" action="{{ route('admin.hero-section.toggle-status') }}" class="d-inline">
            @csrf
            <button type="submit" class="btn-admin {{ $heroSection->is_active ? 'btn-admin-warning' : 'btn-admin-success' }}">
                <i class="fas fa-{{ $heroSection->is_active ? 'eye-slash' : 'eye' }} me-2"></i>
                {{ $heroSection->is_active ? 'Deactivate' : 'Activate' }}
            </button>
        </form>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2 text-primary"></i>
                        Hero Section Preview
                    </h5>
                    <div class="card-actions">
                        <span class="badge {{ $heroSection->is_active ? 'bg-success' : 'bg-secondary' }}">
                            {{ $heroSection->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="hero-preview" style="background: {{ $heroSection->background_color ?? '#f8f9fa' }}; padding: 3rem; border-radius: 8px; position: relative; overflow: hidden;">
                        @if($heroSection->background_image)
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('{{ asset('storage/' . $heroSection->background_image) }}'); background-size: cover; background-position: center; opacity: 0.8;"></div>
                        @endif
                        
                        <div style="position: relative; z-index: 2; text-align: center; color: {{ $heroSection->background_image ? 'white' : '#333' }};">
                            <h1 style="font-size: 2.5rem; font-weight: bold; margin-bottom: 1rem;">{{ $heroSection->title }}</h1>
                            @if($heroSection->subtitle)
                                <h2 style="font-size: 1.5rem; margin-bottom: 1.5rem; opacity: 0.9;">{{ $heroSection->subtitle }}</h2>
                            @endif
                            @if($heroSection->content)
                                <div style="font-size: 1.1rem; margin-bottom: 2rem; opacity: 0.8;">
                                    {!! $heroSection->content !!}
                                </div>
                            @endif
                            @if($heroSection->button_text)
                                <a href="{{ $heroSection->button_url ?? '#' }}" class="btn btn-primary btn-lg">
                                    {{ $heroSection->button_text }}
                                </a>
                            @endif
                        </div>
                    </div>

                    @if($heroSection->images && count($heroSection->images) > 0)
                        <div class="mt-4">
                            <h6>Additional Images:</h6>
                            <div class="row">
                                @foreach($heroSection->image_urls as $imageUrl)
                                    <div class="col-md-3 mb-3">
                                        <img src="{{ $imageUrl }}" alt="Hero image" class="img-fluid rounded shadow-sm">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Section Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Section Name:</strong>
                        <span class="text-muted">{{ $heroSection->section_name }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Type:</strong>
                        <span class="text-muted">{{ $heroSection->item_type }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Sort Order:</strong>
                        <span class="text-muted">{{ $heroSection->sort_order }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Status:</strong>
                        <span class="badge {{ $heroSection->is_active ? 'bg-success' : 'bg-secondary' }}">
                            {{ $heroSection->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>Last Updated:</strong>
                        <span class="text-muted">{{ $heroSection->updated_at->format('M d, Y H:i') }}</span>
                    </div>
                </div>
            </div>

            <div class="admin-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-content">
                        <p><strong>Title:</strong> Keep it concise and impactful (max 60 characters)</p>
                        <p><strong>Subtitle:</strong> Provide additional context or value proposition</p>
                        <p><strong>Content:</strong> Brief description that encourages action</p>
                        <p><strong>Button:</strong> Use action-oriented text like "Get Started", "Learn More"</p>
                        <p><strong>Images:</strong> Use high-quality images that represent your brand</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
.hero-preview {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.help-content p {
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.help-content p:last-child {
    margin-bottom: 0;
}
</style>
@endpush
