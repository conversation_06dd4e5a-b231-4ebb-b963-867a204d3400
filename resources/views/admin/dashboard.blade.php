@extends('admin.layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="admin-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Dashboard Overview</h1>
            <p class="text-muted">Welcome back, {{ Auth::user()->name }}! Here's what's happening today.</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.projects.create') }}" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Project
            </a>
            <a href="{{ route('admin.contact.index') }}" class="btn btn-admin-outline">
                <i class="fas fa-envelope me-2"></i>View Enquiries
            </a>
        </div>
    </div>

    <!-- Key Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ $stats['total_projects'] }}</h4>
                    <small class="text-muted">Total Projects</small>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-arrow-up me-1"></i>{{ $stats['active_projects'] }} Active
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ $stats['new_enquiries'] }}</h4>
                    <small class="text-muted">New Enquiries</small>
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-calendar me-1"></i>{{ $stats['enquiries_today'] }} Today
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ $stats['confirmed_visits'] }}</h4>
                    <small class="text-muted">Confirmed Visits</small>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-clock me-1"></i>{{ $stats['visits_today'] }} Today
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ $stats['active_testimonials'] }}</h4>
                    <small class="text-muted">Active Testimonials</small>
                    <div class="mt-2">
                        <small class="text-primary">
                            <i class="fas fa-quote-left me-1"></i>{{ $stats['total_testimonials'] }} Total
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-primary">{{ $stats['featured_projects'] }}</h5>
                    <small class="text-muted">Featured Projects</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-warning">{{ $stats['enquiries_this_week'] }}</h5>
                    <small class="text-muted">Enquiries This Week</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-info">{{ $stats['visits_this_week'] }}</h5>
                    <small class="text-muted">Visits This Week</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-success">{{ $stats['published_blogs'] }}</h5>
                    <small class="text-muted">Published Blogs</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-primary">{{ $stats['active_services'] }}</h5>
                    <small class="text-muted">Active Services</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h5 class="mb-1 text-secondary">{{ $stats['pending_visits'] }}</h5>
                    <small class="text-muted">Pending Visits</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add Project
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-list me-2"></i>Manage Projects
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-envelope me-2"></i>View Enquiries
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.settings.index') }}" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Recent Projects -->
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        Recent Projects
                    </h5>
                    <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline btn-admin-sm">
                        <i class="fas fa-external-link-alt me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    @if($recentData['recent_projects']->count() > 0)
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Project Name</th>
                                        <th>Location</th>
                                        <th>Starting Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentData['recent_projects'] as $project)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <strong>{{ $project->name }}</strong>
                                                        @if($project->featured)
                                                            <span class="badge badge-warning ms-1">Featured</span>
                                                        @endif
                                                        <br>
                                                        <small class="text-muted">{{ $project->developer }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>{{ $project->location }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $project->city }}</small>
                                            </td>
                                            <td>
                                                <strong class="text-primary">{{ $project->formatted_starting_price }}</strong>
                                            </td>
                                            <td>
                                                @if($project->status === 'upcoming')
                                                    <span class="badge badge-info">Upcoming</span>
                                                @elseif($project->status === 'ongoing')
                                                    <span class="badge badge-warning">Ongoing</span>
                                                @elseif($project->status === 'ready_to_move')
                                                    <span class="badge badge-success">Ready to Move</span>
                                                @else
                                                    <span class="badge badge-secondary">Completed</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a href="{{ $project->project_url }}"
                                                       class="btn-admin btn-admin-success btn-sm"
                                                       target="_blank"
                                                       title="View on Website">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <a href="{{ route('admin.projects.show', $project) }}"
                                                       class="btn-admin btn-admin-outline btn-sm"
                                                       title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.projects.edit', $project) }}"
                                                       class="btn-admin btn-admin-primary btn-sm"
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <h5 class="empty-title">No Projects Yet</h5>
                                <p class="empty-subtitle">Start building your project portfolio by adding your first project.</p>
                                <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary btn-admin-lg">
                                    <i class="fas fa-plus me-2"></i>Add Your First Project
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Analytics & Insights -->
        <div class="col-lg-4">
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2 text-info"></i>
                        Property Analytics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="analytics-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Avg. Property Value</span>
                            <span class="analytics-value text-primary">₹1.2Cr</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="analytics-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Inquiry Rate</span>
                            <span class="analytics-value text-success">68%</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress success" style="width: 68%"></div>
                        </div>
                    </div>

                    <div class="analytics-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Conversion Rate</span>
                            <span class="analytics-value text-warning">24%</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress warning" style="width: 24%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Info -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Laravel Version</small>
                        <div><strong>{{ app()->version() }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">PHP Version</small>
                        <div><strong>{{ PHP_VERSION }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Environment</small>
                        <div>
                            <span class="badge {{ app()->environment('production') ? 'badge-danger' : 'badge-warning' }}">
                                {{ strtoupper(app()->environment()) }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <small class="text-muted">Last Login</small>
                        <div><strong>{{ now()->format('M d, Y H:i') }}</strong></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .d-grid {
        display: grid;
    }
    
    .gap-2 {
        gap: 0.5rem;
    }
</style>
@endpush
