@extends('admin.layouts.app')

@section('title', 'Services Preview Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Services Preview Section</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.services-preview.edit') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit Section
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-8">
                            <div class="services-preview-content">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4>{{ $servicesPreview->title }}</h4>
                                    <form action="{{ route('admin.services-preview.toggle-status') }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm {{ $servicesPreview->is_active ? 'btn-success' : 'btn-secondary' }}">
                                            <i class="fas fa-{{ $servicesPreview->is_active ? 'eye' : 'eye-slash' }}"></i>
                                            {{ $servicesPreview->is_active ? 'Active' : 'Inactive' }}
                                        </button>
                                    </form>
                                </div>

                                @if($servicesPreview->subtitle)
                                    <h5 class="text-muted mb-3">{{ $servicesPreview->subtitle }}</h5>
                                @endif

                                @if($servicesPreview->content)
                                    <div class="content-preview mb-3">
                                        {!! $servicesPreview->content !!}
                                    </div>
                                @endif

                                @if($servicesPreview->button_text && $servicesPreview->button_url)
                                    <div class="button-preview mb-3">
                                        <a href="{{ $servicesPreview->button_url }}" class="btn btn-primary" target="_blank">
                                            {{ $servicesPreview->button_text }}
                                        </a>
                                    </div>
                                @endif

                                <div class="section-details">
                                    <small class="text-muted">
                                        <strong>Section:</strong> {{ $servicesPreview->section_name }} |
                                        <strong>Sort Order:</strong> {{ $servicesPreview->sort_order }} |
                                        <strong>Last Updated:</strong> {{ $servicesPreview->updated_at->format('M d, Y H:i') }}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="section-media">
                                @if($servicesPreview->image)
                                    <div class="image-preview mb-3">
                                        <h6>Section Image:</h6>
                                        <img src="{{ asset('storage/' . $servicesPreview->image) }}" 
                                             alt="Services Preview" 
                                             class="img-fluid rounded" 
                                             style="max-height: 200px;">
                                    </div>
                                @endif

                                @if($servicesPreview->images && is_array($servicesPreview->images) && count($servicesPreview->images) > 0)
                                    <div class="images-preview mb-3">
                                        <h6>Additional Images:</h6>
                                        <div class="row">
                                            @foreach($servicesPreview->images as $image)
                                                <div class="col-6 mb-2">
                                                    <img src="{{ asset('storage/' . $image) }}" 
                                                         alt="Services Image" 
                                                         class="img-fluid rounded" 
                                                         style="max-height: 100px;">
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif

                                @if($servicesPreview->icon)
                                    <div class="icon-preview mb-3">
                                        <h6>Section Icon:</h6>
                                        <i class="{{ $servicesPreview->icon }}" 
                                           style="font-size: 2rem; color: {{ $servicesPreview->icon_color ?? '#007bff' }};"></i>
                                    </div>
                                @endif

                                @if($servicesPreview->background_color)
                                    <div class="background-preview mb-3">
                                        <h6>Background Color:</h6>
                                        <div class="color-swatch" 
                                             style="width: 50px; height: 30px; background-color: {{ $servicesPreview->background_color }}; border: 1px solid #ddd; border-radius: 4px;"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
