@extends('admin.layouts.app')

@section('title', 'View Testimonial')

@section('content')
<div class="admin-header">
    <div class="admin-header-content">
        <h1 class="admin-title">
            <i class="fas fa-quote-left me-2"></i>
            View Testimonial
        </h1>
        <div class="admin-actions">
            <a href="{{ route('admin.testimonials.edit', $testimonial) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                Edit Testimonial
            </a>
            <a href="{{ route('admin.testimonials.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Testimonials
            </a>
        </div>
    </div>
</div>

<div class="admin-content">
    <div class="row">
        <div class="col-lg-8">
            <!-- Testimonial Details -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Testimonial Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <img src="{{ $testimonial->image_url }}" alt="{{ $testimonial->name }}" 
                                 class="img-fluid rounded-circle" style="max-width: 150px;">
                        </div>
                        <div class="col-md-9">
                            <h4 class="mb-2">{{ $testimonial->name }}</h4>
                            @if($testimonial->designation)
                                <p class="text-muted mb-1">{{ $testimonial->designation }}</p>
                            @endif
                            @if($testimonial->company)
                                <p class="text-muted mb-1">{{ $testimonial->company }}</p>
                            @endif
                            @if($testimonial->location)
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $testimonial->location }}
                                </p>
                            @endif
                            <div class="mb-2">
                                {!! $testimonial->star_rating !!}
                                <span class="ms-2 text-muted">({{ $testimonial->rating }}/5)</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Testimonial:</h6>
                        <blockquote class="blockquote">
                            <p class="mb-0">"{{ $testimonial->testimonial }}"</p>
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status & Settings -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Status & Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Status:</label>
                        <div>
                            @if($testimonial->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-danger">Inactive</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Sort Order:</label>
                        <div>
                            <span class="badge bg-secondary">{{ $testimonial->sort_order }}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Rating:</label>
                        <div>
                            {!! $testimonial->star_rating !!}
                            <span class="ms-2">({{ $testimonial->rating }}/5)</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Created:</label>
                        <div>{{ $testimonial->created_at->format('M d, Y \a\t h:i A') }}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Last Updated:</label>
                        <div>{{ $testimonial->updated_at->format('M d, Y \a\t h:i A') }}</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.testimonials.edit', $testimonial) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Edit Testimonial
                        </a>
                        
                        <form action="{{ route('admin.testimonials.destroy', $testimonial) }}" 
                              method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this testimonial?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-1"></i>
                                Delete Testimonial
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
