<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Panel') - {{ config('app.name', 'Hestia Abodes') }}</title>
    <meta name="description" content="@yield('description', 'Admin panel for managing Hestia Abodes website content')">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Custom Admin Styles -->
    <style>
        :root {
            --admin-primary: #cfaa13;
            --admin-secondary: #6c757d;
            --admin-success: #198754;
            --admin-danger: #dc3545;
            --admin-warning: #ffc107;
            --admin-info: #0dcaf0;
            --admin-light: #f8f9fa;
            --admin-dark: #212529;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: #fff;
            border-right: 1px solid #dee2e6;
            z-index: 1000;
            overflow-y: auto;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid #dee2e6;
            background: linear-gradient(135deg, var(--admin-primary), #e6c547);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            margin-right: 0.75rem;
        }

        .brand-text {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }

        .nav-link:hover {
            background-color: #f8f9fa;
            color: var(--admin-primary);
            padding-left: 2rem;
        }

        .nav-link.active {
            background-color: var(--admin-primary);
            color: white;
            border-right: 3px solid #b8941a;
        }

        .nav-icon {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        /* Main Content */
        .admin-main {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            padding: 0;
        }

        .admin-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #212529;
            margin-bottom: 0.5rem;
        }

        .admin-subtitle {
            color: #6c757d;
            margin-bottom: 0;
        }

        .admin-content {
            padding: 0 2rem 2rem;
        }

        .admin-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #f1f3f4;
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--admin-primary) 0%, #ffc107 50%, var(--admin-primary) 100%);
            z-index: 1;
        }

        .admin-card:hover {
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            transform: translateY(-4px);
        }

        .admin-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            padding: 1.75rem 2rem;
            font-weight: 600;
            position: relative;
            margin-top: 4px;
        }

        .admin-card .card-header::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(207, 170, 19, 0.3) 50%, transparent 100%);
        }

        .admin-card .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--admin-primary) 0%, #ffc107 100%);
            opacity: 0.8;
        }

        .admin-card .card-header h5 {
            margin: 0;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .admin-card .card-header h5 i {
            margin-right: 0.75rem;
            color: var(--admin-primary);
        }

        .admin-card .card-body {
            padding: 2rem;
            background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,249,250,0.3) 100%);
            position: relative;
        }

        .admin-card .card-body.p-0 {
            padding: 0;
        }

        .admin-card .card-body.p-0 .table-responsive {
            border-radius: 0 0 16px 16px;
            overflow: hidden;
        }

        /* Table Styles */
        .admin-table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .admin-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
            padding: 1.2rem 1rem;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .admin-table th:first-child {
            border-top-left-radius: 8px;
        }

        .admin-table th:last-child {
            border-top-right-radius: 8px;
        }

        .admin-table td {
            padding: 1.2rem 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
            background-color: #fff;
            transition: background-color 0.2s ease;
        }

        .admin-table tbody tr:hover td {
            background-color: #f8f9fa;
        }

        .admin-table tbody tr:last-child td {
            border-bottom: none;
        }

        .admin-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 8px;
        }

        .admin-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 8px;
        }

        .table-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 0.375rem;
            border: 2px solid #dee2e6;
        }

        .table-image-placeholder {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }

        /* Button Styles */
        .btn-admin {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn-admin::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-admin:hover::before {
            left: 100%;
        }

        .btn-admin-primary {
            background: linear-gradient(135deg, var(--admin-primary) 0%, #b8941a 100%);
            border-color: var(--admin-primary);
            color: white;
            box-shadow: 0 2px 4px rgba(207, 170, 19, 0.2);
        }

        .btn-admin-primary:hover {
            background: linear-gradient(135deg, #b8941a 0%, #a08318 100%);
            border-color: #b8941a;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(207, 170, 19, 0.4);
        }

        .btn-admin-primary:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(207, 170, 19, 0.3);
        }

        .btn-admin-outline {
            background-color: transparent;
            border-color: #6c757d;
            color: #6c757d;
        }

        .btn-admin-outline:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
            transform: translateY(-1px);
        }

        .btn-admin-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-admin-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .btn-admin-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        .btn-admin-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .btn-admin-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .btn-admin-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #212529;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            min-width: 36px;
            height: 36px;
        }

        .btn-admin-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
        }

        /* Action Button Groups */
        .d-flex.gap-2 .btn-admin {
            margin-right: 0;
        }

        .d-flex.gap-2 .btn-admin + .btn-admin {
            margin-left: 0.5rem;
        }

        /* Animations and Loading States */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .loading {
            animation: pulse 1.5s ease-in-out infinite;
        }

        /* Page Content Animation */
        .admin-content > .admin-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .admin-content > .admin-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .admin-content > .admin-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .admin-content > .admin-card:nth-child(4) {
            animation-delay: 0.4s;
        }

        /* Badge Enhancements */
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .badge-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .badge-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }

        .badge-info {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
            color: #212529;
        }

        .badge-danger {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
            color: white;
        }

        /* Empty State Styling */
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
        }

        .empty-icon {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1.5rem;
        }

        .empty-title {
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .empty-subtitle {
            color: #adb5bd;
            margin-bottom: 2rem;
        }

        /* Color Preview */
        .color-preview {
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .color-preview:hover {
            transform: scale(1.1);
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .admin-card .card-header {
                padding: 1.25rem 1.5rem;
            }

            .admin-card .card-body {
                padding: 1.5rem;
            }

            .admin-table th,
            .admin-table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .btn-sm {
                padding: 0.375rem 0.5rem;
                font-size: 0.8rem;
                min-width: 32px;
                height: 32px;
            }
        }

        /* Badge Styles */
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .badge-success {
            background-color: var(--admin-success);
        }

        .badge-warning {
            background-color: var(--admin-warning);
            color: #000;
        }

        .badge-info {
            background-color: var(--admin-info);
            color: #000;
        }

        .badge-secondary {
            background-color: var(--admin-secondary);
        }

        /* Form Styles */
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control:focus {
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 0.2rem rgba(207, 170, 19, 0.25);
        }

        /* Image Preview Styles */
        .current-image-preview img {
            max-width: 100%;
            height: auto;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }

        .current-images {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .current-image {
            flex: 0 0 auto;
        }

        .current-image img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 0.375rem;
            border: 2px solid #dee2e6;
        }

        /* Additional Styles */
        .nav-header {
            padding: 0.5rem 1.5rem;
        }

        .sidebar-footer {
            margin-top: auto;
            padding: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: var(--admin-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 0.75rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            display: block;
            font-weight: 600;
            font-size: 0.875rem;
            color: #212529;
        }

        .user-role {
            display: block;
            font-size: 0.75rem;
            color: #6c757d;
        }

        .logout-btn {
            background: none;
            border: none;
            color: #dc3545;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-sidebar {
                display: none;
            }

            .admin-main {
                margin-left: 0;
            }

            .admin-header,
            .admin-content {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .empty-subtitle {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="{{ route('admin.dashboard') }}" class="sidebar-brand">
                    <i class="fas fa-home brand-logo"></i>
                    <span class="brand-text">Hestia Abodes</span>
                </a>
            </div>

            <nav class="sidebar-nav">
                <div class="nav flex-column">
                    <!-- Dashboard -->
                    <a href="{{ route('admin.dashboard') }}"
                       class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>

                    <!-- Content Management -->
                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">CONTENT MANAGEMENT</small>
                    </div>

                    <a href="{{ route('admin.sliders.index') }}"
                       class="nav-link {{ request()->routeIs('admin.sliders.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-images"></i>
                        <span>Home Slider</span>
                    </a>

                    <!-- Home Page Sections -->
                    <a href="{{ route('admin.about-preview.index') }}"
                       class="nav-link {{ request()->routeIs('admin.about-preview.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-info-circle"></i>
                        <span>About Preview</span>
                    </a>

                    <a href="{{ route('admin.services-preview.index') }}"
                       class="nav-link {{ request()->routeIs('admin.services-preview.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cogs"></i>
                        <span>Services Preview</span>
                    </a>

                    <a href="{{ route('admin.featured-projects.index') }}"
                       class="nav-link {{ request()->routeIs('admin.featured-projects.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-building"></i>
                        <span>Featured Projects</span>
                    </a>

                    <a href="{{ route('admin.testimonials-section.index') }}"
                       class="nav-link {{ request()->routeIs('admin.testimonials-section.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-quote-left"></i>
                        <span>Testimonials Section</span>
                    </a>

                    <a href="{{ route('admin.contact-cta.index') }}"
                       class="nav-link {{ request()->routeIs('admin.contact-cta.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-phone"></i>
                        <span>Contact CTA</span>
                    </a>

                    <a href="{{ route('admin.about-us.index') }}"
                       class="nav-link {{ request()->routeIs('admin.about-us.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-info-circle"></i>
                        <span>About Us</span>
                    </a>

                    <a href="{{ route('admin.founders.index') }}"
                       class="nav-link {{ request()->routeIs('admin.founders.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-users"></i>
                        <span>Founders</span>
                    </a>

                    <!-- Projects & Services -->
                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">PROJECTS & SERVICES</small>
                    </div>

                    <a href="{{ route('admin.projects.index') }}"
                       class="nav-link {{ request()->routeIs('admin.projects.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-building"></i>
                        <span>Projects</span>
                    </a>

                    <a href="{{ route('admin.services.index') }}"
                       class="nav-link {{ request()->routeIs('admin.services.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cogs"></i>
                        <span>Our Services</span>
                    </a>

                    <!-- Communication -->
                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">COMMUNICATION</small>
                    </div>

                    <a href="{{ route('admin.contact.index') }}"
                       class="nav-link {{ request()->routeIs('admin.contact.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-envelope"></i>
                        <span>Contact</span>
                    </a>

                    <a href="{{ route('admin.testimonials.index') }}"
                       class="nav-link {{ request()->routeIs('admin.testimonials.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-quote-left"></i>
                        <span>Testimonials</span>
                    </a>

                    <a href="{{ route('admin.blogs.index') }}"
                       class="nav-link {{ request()->routeIs('admin.blogs.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-blog"></i>
                        <span>Blogs</span>
                    </a>

                    <!-- System -->
                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">SYSTEM</small>
                    </div>

                    <a href="{{ route('admin.settings.index') }}"
                       class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cog"></i>
                        <span>Settings</span>
                    </a>

                    <a href="{{ route('admin.profile.show') }}"
                       class="nav-link {{ request()->routeIs('admin.profile.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-user"></i>
                        <span>Admin User</span>
                    </a>

                    <form method="POST" action="{{ route('admin.logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                            <i class="nav-icon fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main flex-fill">
            <!-- Header Section -->
            @hasSection('header')
                <header class="admin-header fade-in-up">
                    @yield('header')
                </header>
            @else
                <header class="admin-header fade-in-up">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="admin-title">@yield('title', 'Admin Panel')</h1>
                            <p class="admin-subtitle">@yield('subtitle', 'Manage your website content')</p>
                        </div>
                        <div>
                            <a href="{{ route('home') }}" target="_blank" class="btn btn-outline-secondary">
                                <i class="fas fa-external-link-alt me-1"></i>View Website
                            </a>
                        </div>
                    </div>
                </header>
            @endif

            <!-- Content Section -->
            <div class="admin-content fade-in-up">
                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('warning'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ session('warning') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Main Content -->
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Mobile Offcanvas Sidebar -->
    <div class="offcanvas offcanvas-start d-lg-none" tabindex="-1" id="mobileSidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Hestia Abodes</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body p-0">
            <!-- Same navigation as desktop sidebar -->
            <nav class="sidebar-nav">
                <div class="nav flex-column">
                    <a href="{{ route('admin.dashboard') }}"
                       class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>

                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">CONTENT MANAGEMENT</small>
                    </div>

                    <a href="{{ route('admin.sliders.index') }}"
                       class="nav-link {{ request()->routeIs('admin.sliders.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-images"></i>
                        <span>Home Slider</span>
                    </a>

                    <!-- Home Page Sections -->
                    <a href="{{ route('admin.about-preview.index') }}"
                       class="nav-link {{ request()->routeIs('admin.about-preview.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-info-circle"></i>
                        <span>About Preview</span>
                    </a>

                    <a href="{{ route('admin.services-preview.index') }}"
                       class="nav-link {{ request()->routeIs('admin.services-preview.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cogs"></i>
                        <span>Services Preview</span>
                    </a>

                    <a href="{{ route('admin.featured-projects.index') }}"
                       class="nav-link {{ request()->routeIs('admin.featured-projects.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-building"></i>
                        <span>Featured Projects</span>
                    </a>

                    <a href="{{ route('admin.testimonials-section.index') }}"
                       class="nav-link {{ request()->routeIs('admin.testimonials-section.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-quote-left"></i>
                        <span>Testimonials Section</span>
                    </a>

                    <a href="{{ route('admin.contact-cta.index') }}"
                       class="nav-link {{ request()->routeIs('admin.contact-cta.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-phone"></i>
                        <span>Contact CTA</span>
                    </a>

                    <a href="{{ route('admin.about-us.index') }}"
                       class="nav-link {{ request()->routeIs('admin.about-us.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-info-circle"></i>
                        <span>About Us</span>
                    </a>

                    <a href="{{ route('admin.founders.index') }}"
                       class="nav-link {{ request()->routeIs('admin.founders.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-users"></i>
                        <span>Founders</span>
                    </a>

                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">PROJECTS & SERVICES</small>
                    </div>

                    <a href="{{ route('admin.projects.index') }}"
                       class="nav-link {{ request()->routeIs('admin.projects.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-building"></i>
                        <span>Projects</span>
                    </a>

                    <a href="{{ route('admin.services.index') }}"
                       class="nav-link {{ request()->routeIs('admin.services.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cogs"></i>
                        <span>Our Services</span>
                    </a>

                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">COMMUNICATION</small>
                    </div>

                    <a href="{{ route('admin.contact.index') }}"
                       class="nav-link {{ request()->routeIs('admin.contact.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-envelope"></i>
                        <span>Contact</span>
                    </a>

                    <a href="{{ route('admin.testimonials.index') }}"
                       class="nav-link {{ request()->routeIs('admin.testimonials.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-quote-left"></i>
                        <span>Testimonials</span>
                    </a>

                    <a href="{{ route('admin.blogs.index') }}"
                       class="nav-link {{ request()->routeIs('admin.blogs.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-blog"></i>
                        <span>Blogs</span>
                    </a>

                    <div class="nav-header mt-3 mb-2 px-3">
                        <small class="text-muted fw-bold">SYSTEM</small>
                    </div>

                    <a href="{{ route('admin.settings.index') }}"
                       class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cog"></i>
                        <span>Settings</span>
                    </a>

                    <a href="{{ route('admin.profile.show') }}"
                       class="nav-link {{ request()->routeIs('admin.profile.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-user"></i>
                        <span>Admin User</span>
                    </a>

                    <form method="POST" action="{{ route('admin.logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                            <i class="nav-icon fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </nav>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="btn btn-admin-primary d-lg-none position-fixed"
            style="top: 1rem; left: 1rem; z-index: 1100;"
            type="button"
            data-bs-toggle="offcanvas"
            data-bs-target="#mobileSidebar">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CKEditor 5 Latest -->
    <script src="https://cdn.ckeditor.com/ckeditor5/41.1.0/classic/ckeditor.js"></script>

    <!-- Admin CKEditor Configuration -->
    <script src="{{ asset('js/admin-ckeditor.js') }}"></script>

    @stack('scripts')
</body>
</html>