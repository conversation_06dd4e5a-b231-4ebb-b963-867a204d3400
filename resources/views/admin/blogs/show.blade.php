@extends('admin.layouts.app')

@section('title', 'Blog Post Details')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.blogs.index') }}" class="breadcrumb-item">Blogs</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">View Post</span>
    </div>
    <h1 class="page-title">{{ $blog->title }}</h1>
    <p class="page-subtitle">View and manage blog post information</p>
    <div class="page-actions">
        <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit Post
        </a>
        <a href="{{ route('admin.blogs.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <!-- Featured Image -->
            @if($blog->featured_image)
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-image me-2"></i>Featured Image
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <img src="{{ $blog->featured_image_url }}"
                             alt="{{ $blog->title }}"
                             class="featured-image-display">
                    </div>
                </div>
            @endif

            <!-- Post Content -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Post Content
                    </h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-3">{{ $blog->title }}</h2>

                    <div class="blog-meta mb-4">
                        <span class="badge badge-primary me-2">{{ $blog->category }}</span>
                        @if($blog->is_published)
                            <span class="badge badge-success me-2">Published</span>
                        @else
                            <span class="badge badge-warning me-2">Draft</span>
                        @endif
                        @if($blog->is_featured)
                            <span class="badge badge-warning">Featured</span>
                        @endif
                    </div>

                    <div class="blog-meta mb-4">
                        <span class="text-muted">
                            <i class="fas fa-user me-1"></i>{{ $blog->author }}
                        </span>
                        <span class="text-muted mx-2">•</span>
                        <span class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ $blog->formatted_published_date ?? 'Not published' }}
                        </span>
                        <span class="text-muted mx-2">•</span>
                        <span class="text-muted">
                            <i class="fas fa-clock me-1"></i>{{ $blog->read_time }} min read
                        </span>
                    </div>

                    @if($blog->excerpt)
                        <div class="blog-excerpt mb-4 p-3 bg-light rounded">
                            <h6>Excerpt:</h6>
                            <p class="mb-0">{{ $blog->excerpt }}</p>
                        </div>
                    @endif

                    @if($blog->tags && count($blog->tags) > 0)
                        <div class="blog-tags mb-4">
                            <h6>Tags:</h6>
                            @foreach($blog->tags as $tag)
                                <span class="badge badge-secondary me-1">{{ $tag }}</span>
                            @endforeach
                        </div>
                    @endif

                    <div class="blog-content">
                        {!! nl2br(e($blog->content)) !!}
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>SEO Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Meta Title:</strong>
                            <p class="text-muted">{{ $blog->meta_title ?? 'Not set' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Slug:</strong>
                            <p class="text-muted">{{ $blog->slug }}</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Meta Description:</strong>
                        <p class="text-muted">{{ $blog->meta_description ?? 'Not set' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Post Details -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Post Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Status:</strong>
                        @if($blog->is_published)
                            <span class="badge badge-success">Published</span>
                        @else
                            <span class="badge badge-warning">Draft</span>
                        @endif
                    </div>
                    <div class="mb-3">
                        <strong>Author:</strong>
                        <p class="mb-0">{{ $blog->author }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Category:</strong>
                        <span class="badge badge-primary">{{ $blog->category }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Reading Time:</strong>
                        <p class="mb-0">{{ $blog->read_time }} minutes</p>
                    </div>
                    <div class="mb-3">
                        <strong>Created:</strong>
                        <p class="mb-0">{{ $blog->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Published:</strong>
                        <p class="mb-0">{{ $blog->formatted_published_date ?? 'Not published' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong>
                        <p class="mb-0">{{ $blog->updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Featured Image
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ asset('images/default-property.jpg') }}" alt="Featured Image" class="img-fluid rounded">
                </div>
            </div>

            <!-- Categories & Tags -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Categories & Tags
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Category:</strong>
                        <span class="badge badge-primary">Real Estate Tips</span>
                    </div>
                    <div class="mb-3">
                        <strong>Tags:</strong>
                        <div class="mt-2">
                            <span class="badge badge-secondary me-1">Investment</span>
                            <span class="badge badge-secondary me-1">Property</span>
                            <span class="badge badge-secondary me-1">Tips</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Post Statistics -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="stat-number">1,234</h4>
                                <p class="stat-label">Views</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="stat-number">56</h4>
                                <p class="stat-label">Shares</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Post
                        </a>
                        <form action="{{ route('admin.blogs.destroy', $blog) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this post?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100 mt-2">
                                <i class="fas fa-trash me-2"></i>Delete Post
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
.stat-item {
    padding: 1rem 0;
}
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--admin-primary);
    margin-bottom: 0.5rem;
}
.stat-label {
    color: var(--admin-gray-600);
    font-size: 0.875rem;
    margin-bottom: 0;
}
.blog-content {
    line-height: 1.6;
}
.blog-content p {
    margin-bottom: 1rem;
}
</style>
@endpush
