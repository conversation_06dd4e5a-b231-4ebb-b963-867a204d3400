@extends('admin.layouts.app')

@section('title', 'Blog Management')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Blogs</span>
    </div>
    <h1 class="page-title">Blog Management</h1>
    <p class="page-subtitle">Manage your blog posts and articles</p>
    <div class="page-actions">
        <a href="{{ route('admin.blogs.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Blog Post
        </a>
    </div>
@endsection

@section('content')
    <div class="admin-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-blog me-2"></i>
                Blog Posts
                <span class="badge badge-info ms-2">{{ $blogs->total() }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            @if($blogs->count() > 0)
                <div class="table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th width="60">#</th>
                                <th>Featured Image</th>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Published</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($blogs as $index => $blog)
                                <tr>
                                    <td>
                                        <span class="text-muted fw-medium">{{ ($blogs->currentPage() - 1) * $blogs->perPage() + $index + 1 }}</span>
                                    </td>
                                    <td>
                                        @if($blog->featured_image)
                                            <img src="{{ $blog->featured_image_url }}"
                                                 alt="{{ $blog->title }}"
                                                 class="table-image">
                                        @else
                                            <div class="table-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $blog->title }}</strong>
                                            @if($blog->is_featured)
                                                <span class="badge badge-warning ms-1">Featured</span>
                                            @endif
                                        </div>
                                        @if($blog->excerpt)
                                            <small class="text-muted">{{ Str::limit($blog->excerpt, 60) }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $blog->author }}</td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $blog->category }}</span>
                                    </td>
                                    <td>
                                        @if($blog->is_published)
                                            <span class="badge badge-success">Published</span>
                                        @else
                                            <span class="badge badge-warning">Draft</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($blog->published_at)
                                            {{ $blog->published_at->format('M d, Y') }}
                                        @else
                                            <span class="text-muted">Not published</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.blogs.show', $blog) }}"
                                               class="btn-admin btn-admin-outline btn-admin-sm"
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.blogs.edit', $blog) }}"
                                               class="btn-admin btn-admin-primary btn-admin-sm"
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.blogs.destroy', $blog) }}"
                                                  method="POST"
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this blog post?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="btn-admin btn-admin-danger btn-admin-sm"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                @if($blogs->hasPages())
                    <div class="card-footer">
                        {{ $blogs->links() }}
                    </div>
                @endif
            @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-blog"></i>
                    </div>
                    <h4 class="empty-title">No Blog Posts Found</h4>
                    <p class="empty-subtitle">Start by creating your first blog post to share insights and updates with your audience.</p>
                    <a href="{{ route('admin.blogs.create') }}" class="btn-admin btn-admin-primary">
                        <i class="fas fa-plus me-2"></i>Create First Blog Post
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Blog Categories Section -->
    <div class="admin-card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-tags me-2"></i>
                Blog Categories
                <span class="badge badge-info ms-2">0</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h4 class="empty-title">No Categories Found</h4>
                <p class="empty-subtitle">Create categories to organize your blog posts effectively.</p>
                <button class="btn-admin btn-admin-outline" data-bs-toggle="modal" data-bs-target="#categoryModal">
                    <i class="fas fa-plus me-2"></i>Add Category
                </button>
            </div>
        </div>
    </div>

    <!-- Blog Tags Section -->
    <div class="admin-card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-hashtag me-2"></i>
                Blog Tags
                <span class="badge badge-info ms-2">0</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <h4 class="empty-title">No Tags Found</h4>
                <p class="empty-subtitle">Create tags to help readers find related content easily.</p>
                <button class="btn-admin btn-admin-outline" data-bs-toggle="modal" data-bs-target="#tagModal">
                    <i class="fas fa-plus me-2"></i>Add Tag
                </button>
            </div>
        </div>
    </div>
@endsection

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Blog Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" placeholder="e.g., Real Estate Tips">
                    </div>
                    <div class="mb-3">
                        <label for="categorySlug" class="form-label">Slug</label>
                        <input type="text" class="form-control" id="categorySlug" placeholder="real-estate-tips">
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" rows="3" placeholder="Brief description of this category"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Save Category</button>
            </div>
        </div>
    </div>
</div>

<!-- Tag Modal -->
<div class="modal fade" id="tagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Blog Tag</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="tagName" class="form-label">Tag Name</label>
                        <input type="text" class="form-control" id="tagName" placeholder="e.g., Investment">
                    </div>
                    <div class="mb-3">
                        <label for="tagSlug" class="form-label">Slug</label>
                        <input type="text" class="form-control" id="tagSlug" placeholder="investment">
                    </div>
                    <div class="mb-3">
                        <label for="tagColor" class="form-label">Color</label>
                        <input type="color" class="form-control form-control-color" id="tagColor" value="#007bff">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Save Tag</button>
            </div>
        </div>
    </div>
</div>
