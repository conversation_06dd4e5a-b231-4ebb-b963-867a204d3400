@extends('admin.layouts.app')

@section('title', 'Add New Founder')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Add New Founder</h3>
                    <a href="{{ route('admin.founders.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                
                <form action="{{ route('admin.founders.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="card-body">
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="position" class="form-label">Position <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                           id="position" name="position" value="{{ old('position') }}" required>
                                    @error('position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('is_active') is-invalid @enderror" id="is_active" name="is_active" required>
                                        <option value="1" {{ old('is_active', '1') === '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('is_active') === '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Founder Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            <small class="text-muted">Supported formats: JPG, PNG, GIF (Max: 2MB)</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Bio -->
                        <div class="mb-3">
                            <label for="bio" class="form-label">Biography <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('bio') is-invalid @enderror" 
                                      id="bio" name="bio" rows="6" required>{{ old('bio') }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Quote -->
                        <div class="mb-3">
                            <label for="quote" class="form-label">Quote</label>
                            <textarea class="form-control @error('quote') is-invalid @enderror" 
                                      id="quote" name="quote" rows="3">{{ old('quote') }}</textarea>
                            @error('quote')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Principles -->
                        <div class="mb-3">
                            <label class="form-label">Principles</label>
                            <div id="principles-container">
                                @if(old('principles'))
                                    @foreach(old('principles') as $index => $principle)
                                        <div class="principle-item mb-2">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <input type="text" class="form-control" 
                                                           name="principles[{{ $index }}][icon]" 
                                                           placeholder="Icon class" 
                                                           value="{{ $principle['icon'] ?? 'fas fa-check' }}">
                                                </div>
                                                <div class="col-md-9">
                                                    <input type="text" class="form-control" 
                                                           name="principles[{{ $index }}][text]" 
                                                           placeholder="Principle text" 
                                                           value="{{ $principle['text'] ?? '' }}">
                                                </div>
                                                <div class="col-md-1">
                                                    <button type="button" class="btn btn-danger btn-sm remove-principle">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="principle-item mb-2">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <input type="text" class="form-control" 
                                                       name="principles[0][icon]" 
                                                       placeholder="Icon class" 
                                                       value="fas fa-check">
                                            </div>
                                            <div class="col-md-9">
                                                <input type="text" class="form-control" 
                                                       name="principles[0][text]" 
                                                       placeholder="Principle text">
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-danger btn-sm remove-principle">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-sm btn-secondary" id="add-principle">
                                <i class="fas fa-plus"></i> Add Principle
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Founder
                        </button>
                        <a href="{{ route('admin.founders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let principleIndex = {{ old('principles') ? count(old('principles')) : 1 }};
    
    // Add principle
    document.getElementById('add-principle').addEventListener('click', function() {
        const container = document.getElementById('principles-container');
        const newPrinciple = document.createElement('div');
        newPrinciple.className = 'principle-item mb-2';
        newPrinciple.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <input type="text" class="form-control" 
                           name="principles[${principleIndex}][icon]" 
                           placeholder="Icon class" 
                           value="fas fa-check">
                </div>
                <div class="col-md-9">
                    <input type="text" class="form-control" 
                           name="principles[${principleIndex}][text]" 
                           placeholder="Principle text">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-principle">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newPrinciple);
        principleIndex++;
    });
    
    // Remove principle
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-principle')) {
            const principleItem = e.target.closest('.principle-item');
            if (document.querySelectorAll('.principle-item').length > 1) {
                principleItem.remove();
            }
        }
    });
});
</script>
@endsection
