@extends('admin.layouts.app')

@section('title', 'View Founder')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Founder Details: {{ $founder->name }}</h3>
                    <div>
                        <a href="{{ route('admin.founders.edit', $founder) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.founders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Left Column - Image and Basic Info -->
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                @if($founder->hasImage())
                                    <img src="{{ $founder->image_url }}" alt="{{ $founder->name }}" 
                                         class="img-fluid rounded-3 shadow-lg" style="max-width: 100%; height: auto;">
                                @else
                                    <div class="bg-secondary rounded-3 d-flex align-items-center justify-content-center shadow-lg" 
                                         style="width: 100%; height: 300px;">
                                        <i class="fas fa-user fa-5x text-white"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $founder->name }}</h5>
                                    <p class="card-text text-muted">{{ $founder->position }}</p>
                                    
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Sort Order:</strong>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-info">{{ $founder->sort_order }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <strong>Status:</strong>
                                        </div>
                                        <div class="col-6">
                                            @if($founder->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-danger">Inactive</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <strong>Created:</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">{{ $founder->created_at->format('M d, Y') }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <strong>Updated:</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">{{ $founder->updated_at->format('M d, Y') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Column - Content -->
                        <div class="col-md-8">
                            <!-- Biography -->
                            <div class="mb-4">
                                <h5>Biography</h5>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="founder-bio">
                                            {!! nl2br(e($founder->bio)) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Quote -->
                            @if($founder->quote)
                                <div class="mb-4">
                                    <h5>Quote</h5>
                                    <div class="card">
                                        <div class="card-body">
                                            <blockquote class="blockquote">
                                                <p class="mb-0">"{{ $founder->quote }}"</p>
                                                <footer class="blockquote-footer mt-2">
                                                    <cite title="Source Title">{{ $founder->name }}</cite>
                                                </footer>
                                            </blockquote>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            
                            <!-- Principles -->
                            @if($founder->principles && count($founder->principles) > 0)
                                <div class="mb-4">
                                    <h5>Principles</h5>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="founder-principles">
                                                @foreach($founder->principles as $principle)
                                                    <div class="principle-item d-flex align-items-center mb-3">
                                                        <div class="principle-icon me-3">
                                                            <i class="{{ $principle['icon'] ?? 'fas fa-check' }} text-primary"></i>
                                                        </div>
                                                        <div class="principle-text">
                                                            <span>{{ $principle['text'] }}</span>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ route('admin.founders.edit', $founder) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Founder
                            </a>
                        </div>
                        <div>
                            <form action="{{ route('admin.founders.destroy', $founder) }}" method="POST" class="d-inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this founder? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete Founder
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.founder-bio {
    line-height: 1.6;
    font-size: 1rem;
}

.principle-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.principle-item:last-child {
    border-bottom: none;
}

.principle-icon {
    width: 30px;
    text-align: center;
}

.principle-text {
    flex: 1;
}
</style>
@endsection
