@extends('admin.layouts.app')

@section('title', 'View Slider')

@section('content')
<div class="admin-header">
    <div class="admin-header-content">
        <h1 class="admin-title">
            <i class="fas fa-eye me-2"></i>
            View Slider
        </h1>
        <div class="admin-actions">
            <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                Edit Slider
            </a>
            <a href="{{ route('admin.sliders.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Sliders
            </a>
        </div>
    </div>
</div>

<div class="admin-content">
    <div class="row">
        <div class="col-lg-8">
            <!-- Slider Image -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Slider Image</h5>
                </div>
                <div class="card-body">
                    <img src="{{ $slider->image_url }}" alt="{{ $slider->title }}" 
                         class="img-fluid rounded w-100" style="max-height: 400px; object-fit: cover;">
                </div>
            </div>

            <!-- Content -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Content</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-semibold">Title</label>
                            <p class="mb-0">{{ $slider->title }}</p>
                        </div>
                        
                        @if($slider->subtitle)
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-semibold">Subtitle</label>
                            <p class="mb-0">{{ $slider->subtitle }}</p>
                        </div>
                        @endif
                        
                        @if($slider->description)
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-semibold">Description</label>
                            <p class="mb-0">{{ $slider->description }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            @if($slider->button_text || $slider->button_text_2)
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Action Buttons</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($slider->button_text)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Button 1</label>
                            <div class="border rounded p-3">
                                <div class="mb-2">
                                    <strong>Text:</strong> {{ $slider->button_text }}
                                </div>
                                @if($slider->button_link)
                                <div>
                                    <strong>Link:</strong> 
                                    <a href="{{ $slider->button_link }}" target="_blank" class="text-primary">
                                        {{ $slider->button_link }}
                                        <i class="fas fa-external-link-alt ms-1"></i>
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($slider->button_text_2)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Button 2</label>
                            <div class="border rounded p-3">
                                <div class="mb-2">
                                    <strong>Text:</strong> {{ $slider->button_text_2 }}
                                </div>
                                @if($slider->button_link_2)
                                <div>
                                    <strong>Link:</strong> 
                                    <a href="{{ $slider->button_link_2 }}" target="_blank" class="text-primary">
                                        {{ $slider->button_link_2 }}
                                        <i class="fas fa-external-link-alt ms-1"></i>
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Settings -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Settings</h5>
                </div>
                <div class="card-body">

                    
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Sort Order</label>
                        <div>
                            <span class="badge bg-secondary fs-6">{{ $slider->sort_order }}</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Status</label>
                        <div>
                            @if($slider->is_active)
                                <span class="badge bg-success fs-6">Active</span>
                            @else
                                <span class="badge bg-danger fs-6">Inactive</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Created</label>
                        <p class="mb-0">{{ $slider->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Last Updated</label>
                        <p class="mb-0">{{ $slider->updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            @if($slider->meta_title || $slider->meta_description)
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">SEO Information</h5>
                </div>
                <div class="card-body">
                    @if($slider->meta_title)
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Meta Title</label>
                        <p class="mb-0">{{ $slider->meta_title }}</p>
                    </div>
                    @endif
                    
                    @if($slider->meta_description)
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Meta Description</label>
                        <p class="mb-0">{{ $slider->meta_description }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="admin-card">
                <div class="card-body">
                    <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i>
                        Edit Slider
                    </a>
                    
                    <form action="{{ route('admin.sliders.destroy', $slider) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this slider?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100 mb-2">
                            <i class="fas fa-trash me-1"></i>
                            Delete Slider
                        </button>
                    </form>
                    
                    <a href="{{ route('admin.sliders.index') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Sliders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
