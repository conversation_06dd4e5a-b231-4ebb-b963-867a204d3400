@extends('admin.layouts.app')

@section('title', 'Home Slider')
@section('page-title', 'Home Slider Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4>Home Slider</h4>
    <a href="{{ route('admin.sliders.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Slider
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($sliders->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="100">Image</th>
                            <th>Title</th>
                            <th>Subtitle</th>
                            <th width="80">Sort Order</th>
                            <th width="80">Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sliders as $slider)
                        <tr>
                            <td>
                                @if($slider->image_path)
                                    <img src="{{ asset('storage/' . $slider->image_path) }}"
                                         alt="{{ $slider->title }}"
                                         class="img-thumbnail"
                                         style="width: 80px; height: 50px; object-fit: cover;">
                                @else
                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 50px; border-radius: 4px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $slider->title }}</strong>
                                @if($slider->button_text)
                                    <br><small class="text-muted">
                                        <i class="fas fa-link me-1"></i>{{ $slider->button_text }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                {{ Str::limit($slider->subtitle, 50) }}
                                @if($slider->description)
                                    <br><small class="text-muted">{{ Str::limit($slider->description, 60) }}</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $slider->sort_order ?? 0 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $slider->is_active ? 'success' : 'secondary' }}">
                                    {{ $slider->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.sliders.show', $slider) }}"
                                       class="btn btn-sm btn-outline-info"
                                       title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.sliders.edit', $slider) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.sliders.destroy', $slider) }}"
                                          method="POST"
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this slider? This action cannot be undone.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="btn btn-sm btn-outline-danger"
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            @if($sliders->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $sliders->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No sliders found</h5>
                <p class="text-muted">Create your first slider to get started.</p>
                <a href="{{ route('admin.sliders.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Slider
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
