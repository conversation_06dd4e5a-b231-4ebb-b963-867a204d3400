@extends('admin.layouts.app')

@section('title', 'View Slider')
@section('page-title', 'Slider Details')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Slider Information</h5>
                <div>
                    <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                    <a href="{{ route('admin.sliders.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Back
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Title</h6>
                        <p class="mb-3">{{ $slider->title }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Subtitle</h6>
                        <p class="mb-3">{{ $slider->subtitle ?: 'Not set' }}</p>
                    </div>
                </div>

                @if($slider->description)
                <div class="mb-3">
                    <h6 class="text-muted">Description</h6>
                    <p>{{ $slider->description }}</p>
                </div>
                @endif

                @if($slider->image_path)
                <div class="mb-3">
                    <h6 class="text-muted">Slider Image</h6>
                    <img src="{{ asset('storage/' . $slider->image_path) }}"
                         alt="{{ $slider->title }}"
                         class="img-fluid rounded border"
                         style="max-width: 100%; max-height: 400px; object-fit: cover;">
                </div>
                @endif
                
                <div class="row">
                    @if($slider->button_text)
                    <div class="col-md-6">
                        <h6 class="text-muted">Primary Button</h6>
                        <p class="mb-1"><strong>Text:</strong> {{ $slider->button_text }}</p>
                        @if($slider->button_link)
                            <p class="mb-3"><strong>URL:</strong> <a href="{{ $slider->button_link }}" target="_blank">{{ $slider->button_link }}</a></p>
                        @endif
                    </div>
                    @endif

                    @if($slider->button_text_2)
                    <div class="col-md-6">
                        <h6 class="text-muted">Secondary Button</h6>
                        <p class="mb-1"><strong>Text:</strong> {{ $slider->button_text_2 }}</p>
                        @if($slider->button_link_2)
                            <p class="mb-3"><strong>URL:</strong> <a href="{{ $slider->button_link_2 }}" target="_blank">{{ $slider->button_link_2 }}</a></p>
                        @endif
                    </div>
                    @endif
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-muted">Sort Order</h6>
                        <p class="mb-3">{{ $slider->sort_order ?? 0 }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Status</h6>
                        <p class="mb-3">
                            <span class="badge bg-{{ $slider->is_active ? 'success' : 'secondary' }}">
                                {{ $slider->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Created</h6>
                        <p class="mb-3">{{ $slider->created_at->format('M d, Y') }}</p>
                    </div>
                </div>

                @if($slider->meta_title || $slider->meta_description)
                <hr>
                <h6 class="text-muted mb-3">SEO Information</h6>
                <div class="row">
                    @if($slider->meta_title)
                    <div class="col-md-6">
                        <h6 class="text-muted small">Meta Title</h6>
                        <p class="mb-3">{{ $slider->meta_title }}</p>
                    </div>
                    @endif
                    @if($slider->meta_description)
                    <div class="col-md-6">
                        <h6 class="text-muted small">Meta Description</h6>
                        <p class="mb-3">{{ $slider->meta_description }}</p>
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Slider
                    </a>
                    <a href="{{ route('admin.sliders.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add New Slider
                    </a>
                    <form action="{{ route('admin.sliders.destroy', $slider) }}"
                          method="POST"
                          onsubmit="return confirm('Are you sure you want to delete this slider? This action cannot be undone.')"
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-2"></i>Delete Slider
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Slider Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ $slider->sort_order ?? 0 }}</h4>
                        <small class="text-muted">Display Order</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-{{ $slider->is_active ? 'success' : 'secondary' }}">
                            {{ $slider->is_active ? 'ON' : 'OFF' }}
                        </h4>
                        <small class="text-muted">Status</small>
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <strong>Last Updated:</strong><br>
                    {{ $slider->updated_at->format('M d, Y \a\t g:i A') }}
                </small>
            </div>
        </div>
    </div>
</div>
@endsection
