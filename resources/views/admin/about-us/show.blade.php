@extends('admin.layouts.app')

@section('title', 'View About Us Section')
@section('page-title', 'About Us Section Details')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ $aboutUsSection->title }}</h5>
                <span class="badge bg-{{ $aboutUsSection->is_active ? 'success' : 'secondary' }}">
                    {{ $aboutUsSection->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>
            <div class="card-body">
                @if($aboutUsSection->subtitle)
                    <h6 class="text-muted mb-3">{{ $aboutUsSection->subtitle }}</h6>
                @endif
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <strong>Section Key:</strong>
                        <code>{{ $aboutUsSection->section_key }}</code>
                    </div>
                    <div class="col-md-3">
                        <strong>Content Type:</strong>
                        <span class="badge bg-{{ $aboutUsSection->content_type === 'icon' ? 'info' : ($aboutUsSection->content_type === 'image' ? 'success' : 'secondary') }}">
                            {{ ucfirst($aboutUsSection->content_type) }}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>Sort Order:</strong>
                        {{ $aboutUsSection->sort_order }}
                    </div>
                </div>
                
                @if($aboutUsSection->hasImage() || $aboutUsSection->hasIcon())
                    <div class="text-center mb-4">
                        @if($aboutUsSection->hasImage())
                            <img src="{{ $aboutUsSection->image_url }}" 
                                 alt="{{ $aboutUsSection->title }}" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 300px;">
                        @elseif($aboutUsSection->hasIcon())
                            <div class="p-4 bg-light rounded d-inline-block">
                                <i class="{{ $aboutUsSection->icon_class }} fa-4x text-primary"></i>
                            </div>
                            <p class="text-muted mt-2">
                                <code>{{ $aboutUsSection->icon_class }}</code>
                            </p>
                        @endif
                    </div>
                @endif
                
                <div class="mb-4">
                    <h6>Description:</h6>
                    <div class="border rounded p-3 bg-light">
                        {!! $aboutUsSection->description !!}
                    </div>
                </div>
                
                <div class="row text-muted small">
                    <div class="col-md-6">
                        <strong>Created:</strong> {{ $aboutUsSection->created_at->format('M d, Y h:i A') }}
                    </div>
                    <div class="col-md-6">
                        <strong>Last Updated:</strong> {{ $aboutUsSection->updated_at->format('M d, Y h:i A') }}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.about-us.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to List
                    </a>
                    <div>
                        <a href="{{ route('admin.about-us.edit', $aboutUsSection) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>Edit Section
                        </a>
                        <form action="{{ route('admin.about-us.destroy', $aboutUsSection) }}" 
                              method="POST" 
                              style="display: inline;"
                              onsubmit="return confirm('Are you sure you want to delete this section? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger ms-2">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Section Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">{{ strlen(strip_tags($aboutUsSection->description)) }}</h4>
                            <small class="text-muted">Characters</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">{{ str_word_count(strip_tags($aboutUsSection->description)) }}</h4>
                            <small class="text-muted">Words</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($aboutUsSection->is_active)
                        <form action="{{ route('admin.about-us.update', $aboutUsSection) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $aboutUsSection->title }}">
                            <input type="hidden" name="subtitle" value="{{ $aboutUsSection->subtitle }}">
                            <input type="hidden" name="description" value="{{ $aboutUsSection->description }}">
                            <input type="hidden" name="content_type" value="{{ $aboutUsSection->content_type }}">
                            <input type="hidden" name="icon_class" value="{{ $aboutUsSection->icon_class }}">
                            <input type="hidden" name="sort_order" value="{{ $aboutUsSection->sort_order }}">
                            <input type="hidden" name="is_active" value="0">
                            <button type="submit" class="btn btn-warning btn-sm">
                                <i class="fas fa-eye-slash me-1"></i>Deactivate Section
                            </button>
                        </form>
                    @else
                        <form action="{{ route('admin.about-us.update', $aboutUsSection) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $aboutUsSection->title }}">
                            <input type="hidden" name="subtitle" value="{{ $aboutUsSection->subtitle }}">
                            <input type="hidden" name="description" value="{{ $aboutUsSection->description }}">
                            <input type="hidden" name="content_type" value="{{ $aboutUsSection->content_type }}">
                            <input type="hidden" name="icon_class" value="{{ $aboutUsSection->icon_class }}">
                            <input type="hidden" name="sort_order" value="{{ $aboutUsSection->sort_order }}">
                            <input type="hidden" name="is_active" value="1">
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-eye me-1"></i>Activate Section
                            </button>
                        </form>
                    @endif
                    
                    <a href="{{ route('admin.about-us.create') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Add New Section
                    </a>
                </div>
            </div>
        </div>
        
        @if($aboutUsSection->additional_data)
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Additional Data</h5>
            </div>
            <div class="card-body">
                <pre class="small">{{ json_encode($aboutUsSection->additional_data, JSON_PRETTY_PRINT) }}</pre>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
