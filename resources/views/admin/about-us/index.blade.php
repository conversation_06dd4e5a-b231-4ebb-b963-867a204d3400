@extends('admin.layouts.app')

@section('title', 'About Us Sections')
@section('page-title', 'About Us Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">About Us Sections</h5>
                <a href="{{ route('admin.about-us.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Add New Section
                </a>
            </div>
            <div class="card-body">
                @if($sections->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Preview</th>
                                    <th>Title</th>
                                    <th>Section Key</th>
                                    <th>Type</th>
                                    <th>Order</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sections as $section)
                                <tr>
                                    <td>
                                        @if($section->hasImage())
                                            <img src="{{ $section->image_url }}" 
                                                 alt="{{ $section->title }}" 
                                                 class="img-thumbnail" 
                                                 style="width: 60px; height: 40px; object-fit: cover;">
                                        @elseif($section->hasIcon())
                                            <div class="text-center p-2 bg-light rounded" style="width: 60px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                                <i class="{{ $section->icon_class }} text-primary"></i>
                                            </div>
                                        @else
                                            <div class="text-center p-2 bg-light rounded" style="width: 60px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-file-text text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $section->title }}</strong>
                                        @if($section->subtitle)
                                            <br><small class="text-muted">{{ Str::limit($section->subtitle, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <code>{{ $section->section_key }}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $section->content_type === 'icon' ? 'info' : ($section->content_type === 'image' ? 'success' : 'secondary') }}">
                                            {{ ucfirst($section->content_type) }}
                                        </span>
                                    </td>
                                    <td>{{ $section->sort_order }}</td>
                                    <td>
                                        <span class="badge bg-{{ $section->is_active ? 'success' : 'secondary' }}">
                                            {{ $section->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.about-us.show', $section) }}" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.about-us.edit', $section) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.about-us.destroy', $section) }}" 
                                                  method="POST" 
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this section?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $sections->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No About Us sections found</h5>
                        <p class="text-muted">Create your first section to get started.</p>
                        <a href="{{ route('admin.about-us.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Add First Section
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
