@extends('admin.layouts.app')

@section('title', 'Edit About Us Section')
@section('page-title', 'Edit About Us Section')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Section Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.about-us.update', $aboutUsSection) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $aboutUsSection->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $aboutUsSection->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" value="{{ old('subtitle', $aboutUsSection->subtitle) }}">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="8" required>{{ old('description', $aboutUsSection->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="content_type" class="form-label">Content Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('content_type') is-invalid @enderror" 
                                        id="content_type" name="content_type" required>
                                    <option value="">Select Content Type</option>
                                    <option value="text" {{ old('content_type', $aboutUsSection->content_type) === 'text' ? 'selected' : '' }}>Text Only</option>
                                    <option value="icon" {{ old('content_type', $aboutUsSection->content_type) === 'icon' ? 'selected' : '' }}>With Icon</option>
                                    <option value="image" {{ old('content_type', $aboutUsSection->content_type) === 'image' ? 'selected' : '' }}>With Image</option>
                                </select>
                                @error('content_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('is_active') is-invalid @enderror" id="is_active" name="is_active" required>
                                    <option value="1" {{ old('is_active', $aboutUsSection->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active', $aboutUsSection->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                </select>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Icon Selection -->
                    <div id="icon-section" class="mb-3" style="display: none;">
                        <label for="icon_class" class="form-label">Select Icon</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('icon_class') is-invalid @enderror"
                                   id="icon_class" name="icon_class" value="{{ old('icon_class', $aboutUsSection->icon_class) }}"
                                   placeholder="e.g., fas fa-home, fas fa-shield-alt" readonly>
                            <button type="button" class="btn btn-outline-primary" id="icon-picker-btn">
                                <i class="fas fa-icons"></i> Choose Icon
                            </button>
                        </div>

                        <!-- Icon Preview -->
                        <div class="mt-3" id="icon-preview" style="display: {{ $aboutUsSection->icon_class ? 'block' : 'none' }};">
                            <div class="p-3 bg-light rounded text-center">
                                <i id="preview-icon" class="{{ $aboutUsSection->icon_class }} fa-3x" style="color: {{ $aboutUsSection->icon_color ?? '#cfaa13' }};"></i>
                                <div class="mt-2">
                                    <small class="text-muted">Icon Preview</small>
                                </div>
                            </div>
                        </div>

                        @error('icon_class')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Icon Color Section -->
                    <div id="icon-color-section" class="mb-3" style="display: none;">
                        <label for="icon_color" class="form-label">Icon Color</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="color" class="form-control form-control-color @error('icon_color') is-invalid @enderror"
                                       id="icon_color" name="icon_color"
                                       value="{{ old('icon_color', $aboutUsSection->icon_color ?? '#cfaa13') }}">
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="icon_color_hex"
                                       value="{{ old('icon_color', $aboutUsSection->icon_color ?? '#cfaa13') }}"
                                       placeholder="#cfaa13">
                            </div>
                        </div>
                        @error('icon_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Image Upload -->
                    <div id="image-section" class="mb-3" style="display: none;">
                        <label for="image" class="form-label">Section Image</label>
                        
                        @if($aboutUsSection->hasImage())
                            <div class="mb-2">
                                <img src="{{ $aboutUsSection->image_url }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                <p class="text-muted small">Current image</p>
                            </div>
                        @endif
                        
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        <div class="form-text">
                            @if($aboutUsSection->hasImage())
                                Leave empty to keep current image.
                            @endif
                            Recommended size: 800x600px. Max file size: 2MB
                        </div>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror

                        <!-- Image Position -->
                        <div class="mt-3">
                            <label for="image_position" class="form-label">Image Position</label>
                            <select class="form-select @error('image_position') is-invalid @enderror"
                                    id="image_position" name="image_position">
                                <option value="left" {{ old('image_position', $aboutUsSection->image_position) === 'left' ? 'selected' : '' }}>Left of Text</option>
                                <option value="right" {{ old('image_position', $aboutUsSection->image_position) === 'right' ? 'selected' : '' }}>Right of Text</option>
                                <option value="top" {{ old('image_position', $aboutUsSection->image_position) === 'top' ? 'selected' : '' }}>Above Text</option>
                                <option value="bottom" {{ old('image_position', $aboutUsSection->image_position) === 'bottom' ? 'selected' : '' }}>Below Text</option>
                            </select>
                            @error('image_position')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Subcontent Section -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="columns_count" class="form-label">Number of Columns</label>
                                <select class="form-select @error('columns_count') is-invalid @enderror"
                                        id="columns_count" name="columns_count">
                                    <option value="1" {{ old('columns_count', $aboutUsSection->columns_count) == 1 ? 'selected' : '' }}>1 Column</option>
                                    <option value="2" {{ old('columns_count', $aboutUsSection->columns_count) == 2 ? 'selected' : '' }}>2 Columns</option>
                                    <option value="3" {{ old('columns_count', $aboutUsSection->columns_count) == 3 ? 'selected' : '' }}>3 Columns</option>
                                    <option value="4" {{ old('columns_count', $aboutUsSection->columns_count) == 4 ? 'selected' : '' }}>4 Columns</option>
                                </select>
                                @error('columns_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Subcontent Fields -->
                    <div id="subcontent-section" class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6>Subcontent Items</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-subcontent">
                                <i class="fas fa-plus me-1"></i>Add Item
                            </button>
                        </div>
                        <div id="subcontent-container">
                            <!-- Existing subcontent items will be loaded here -->
                        </div>
                    </div>

                    <!-- Background Section -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="fas fa-palette me-2"></i>Background Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-4">
                                <label for="background_type" class="form-label">Background Type</label>
                                <select class="form-select @error('background_type') is-invalid @enderror"
                                        id="background_type" name="background_type">
                                    <option value="none" {{ old('background_type', $aboutUsSection->background_type ?? 'none') === 'none' ? 'selected' : '' }}>None</option>
                                    <option value="color" {{ old('background_type', $aboutUsSection->background_type) === 'color' ? 'selected' : '' }}>Color</option>
                                    <option value="image" {{ old('background_type', $aboutUsSection->background_type) === 'image' ? 'selected' : '' }}>Image</option>
                                </select>
                                @error('background_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4" id="background-color-section" style="display: none;">
                                <label for="background_color" class="form-label">Background Color</label>
                                <div class="input-group">
                                    <input type="text" class="form-control @error('background_color') is-invalid @enderror"
                                           id="background_color" name="background_color"
                                           value="{{ old('background_color', $aboutUsSection->background_color ?? '#ffffff') }}"
                                           placeholder="#ffffff">
                                    <input type="color" class="form-control form-control-color"
                                           id="background_color_picker"
                                           value="{{ old('background_color', $aboutUsSection->background_color ?? '#ffffff') }}"
                                           style="max-width: 50px;">
                                </div>
                                @error('background_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4" id="background-image-section" style="display: none;">
                                <label for="background_image" class="form-label">Background Image</label>
                                <input type="file" class="form-control @error('background_image') is-invalid @enderror"
                                       id="background_image" name="background_image" accept="image/*">
                                @if($aboutUsSection->background_image)
                                    <div class="mt-2">
                                        <small class="text-muted">Current: {{ basename($aboutUsSection->background_image) }}</small>
                                        <img src="{{ asset('storage/' . $aboutUsSection->background_image) }}"
                                             alt="Current background" class="img-thumbnail ms-2" style="max-height: 50px;">
                                    </div>
                                @endif
                                <div class="form-text">Recommended size: 1920x1080px. Max file size: 2MB</div>
                                @error('background_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>



                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.about-us.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.about-us.show', $aboutUsSection) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Section
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Current Section</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    @if($aboutUsSection->hasImage())
                        <img src="{{ $aboutUsSection->image_url }}" alt="{{ $aboutUsSection->title }}" class="img-fluid rounded">
                    @elseif($aboutUsSection->hasIcon())
                        <div class="p-4 bg-light rounded">
                            <i class="{{ $aboutUsSection->icon_class }} fa-3x text-primary"></i>
                        </div>
                    @endif
                </div>
                
                <h6>{{ $aboutUsSection->title }}</h6>
                @if($aboutUsSection->subtitle)
                    <p class="text-muted">{{ $aboutUsSection->subtitle }}</p>
                @endif
                <div class="small">
                    {!! Str::limit(strip_tags($aboutUsSection->description), 150) !!}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Popular Icons</h5>
            </div>
            <div class="card-body">
                <div class="row g-2" id="popular-icons">
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-home">
                            <i class="fas fa-home"></i><br><small>Home</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-shield-alt">
                            <i class="fas fa-shield-alt"></i><br><small>Shield</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-lightbulb">
                            <i class="fas fa-lightbulb"></i><br><small>Idea</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-bullseye">
                            <i class="fas fa-bullseye"></i><br><small>Target</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-heart">
                            <i class="fas fa-heart"></i><br><small>Heart</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-users">
                            <i class="fas fa-users"></i><br><small>Users</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Icon Picker Modal -->
<div class="modal fade" id="iconPickerModal" tabindex="-1" aria-labelledby="iconPickerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="iconPickerModalLabel">Choose an Icon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="icon-search" placeholder="Search icons...">
                </div>
                <div class="row" id="icon-grid">
                    <!-- Icons will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .icon-option {
        transition: all 0.2s ease;
    }

    .icon-option:hover {
        background-color: #f8f9fa;
        border-color: #cfaa13 !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .icon-option:hover i {
        color: #cfaa13;
    }

    .subcontent-icon-preview {
        background: #f8f9fa;
        border-radius: 0.375rem;
        padding: 0.5rem;
    }

    #icon-search {
        border: 2px solid #dee2e6;
    }

    #icon-search:focus {
        border-color: #cfaa13;
        box-shadow: 0 0 0 0.2rem rgba(207, 170, 19, 0.25);
    }

    .form-control-color {
        width: 100%;
        height: 38px;
    }
</style>
@endpush

@section('scripts')
<!-- Page-specific scripts -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor using global configuration
    if (typeof window.initAdminCKEditor === 'function') {
        window.initAdminCKEditor('#description')
            .then(editor => {
                console.log('About Us CKEditor initialized successfully');
                window.descriptionEditor = editor;
            })
            .catch(error => {
                console.error('About Us CKEditor initialization error:', error);
            });
    } else {
        console.error('Admin CKEditor configuration not loaded');
    }
    
    // Content type change handler
    const contentTypeSelect = document.getElementById('content_type');
    const iconSection = document.getElementById('icon-section');
    const imageSection = document.getElementById('image-section');
    const iconColorSection = document.getElementById('icon-color-section');

    console.log('Content type select:', contentTypeSelect);
    console.log('Icon section:', iconSection);
    console.log('Image section:', imageSection);
    console.log('Icon color section:', iconColorSection);

    function toggleContentSections() {
        const selectedType = contentTypeSelect.value;
        console.log('Content type changed to:', selectedType);

        // Hide all sections first
        if (iconSection) {
            iconSection.style.display = 'none';
            console.log('Icon section hidden');
        }
        if (imageSection) {
            imageSection.style.display = 'none';
            console.log('Image section hidden');
        }
        if (iconColorSection) {
            iconColorSection.style.display = 'none';
            console.log('Icon color section hidden');
        }

        // Show relevant section
        if (selectedType === 'icon') {
            if (iconSection) {
                iconSection.style.display = 'block';
                console.log('Icon section shown');
            }
            if (iconColorSection) {
                iconColorSection.style.display = 'block';
                console.log('Icon color section shown');
            }
        } else if (selectedType === 'image') {
            if (imageSection) {
                imageSection.style.display = 'block';
                console.log('Image section shown');
            }
        }
    }

    contentTypeSelect.addEventListener('change', toggleContentSections);

    // Initialize on page load
    toggleContentSections();

    console.log('About Us edit page JavaScript loaded');
    console.log('Icon picker button:', iconPickerBtn);
    console.log('Content type select:', contentTypeSelect);

    // Icon picker functionality
    const iconPickerBtn = document.getElementById('icon-picker-btn');
    const iconClassInput = document.getElementById('icon_class');
    const iconPreview = document.getElementById('icon-preview');
    const previewIcon = document.getElementById('preview-icon');
    const iconColorInput = document.getElementById('icon_color');
    const iconColorHex = document.getElementById('icon_color_hex');

    // Check if elements exist before adding event listeners
    if (!iconPickerBtn || !iconClassInput || !iconPreview || !previewIcon) {
        console.error('Icon picker elements not found');
        return;
    }

    // Popular Font Awesome icons
    const popularIcons = [
        'fas fa-home', 'fas fa-user', 'fas fa-users', 'fas fa-heart', 'fas fa-star',
        'fas fa-shield-alt', 'fas fa-lightbulb', 'fas fa-bullseye', 'fas fa-trophy',
        'fas fa-rocket', 'fas fa-building', 'fas fa-handshake', 'fas fa-tools',
        'fas fa-cog', 'fas fa-chart-line', 'fas fa-globe', 'fas fa-phone',
        'fas fa-envelope', 'fas fa-map-marker-alt', 'fas fa-calendar',
        'fas fa-clock', 'fas fa-check-circle', 'fas fa-thumbs-up', 'fas fa-eye',
        'fas fa-diamond', 'fas fa-crown', 'fas fa-medal', 'fas fa-award',
        'fas fa-leaf', 'fas fa-tree', 'fas fa-sun', 'fas fa-moon'
    ];

    iconPickerBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Icon picker button clicked');

        const modal = new bootstrap.Modal(document.getElementById('iconPickerModal'));
        const iconGrid = document.getElementById('icon-grid');

        if (!iconGrid) {
            console.error('Icon grid not found');
            return;
        }

        // Clear and populate icon grid
        iconGrid.innerHTML = '';
        popularIcons.forEach(iconClass => {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'col-2 mb-3';
            iconDiv.innerHTML = `
                <div class="text-center p-2 border rounded icon-option" style="cursor: pointer;" data-icon="${iconClass}">
                    <i class="${iconClass} fa-2x mb-1"></i>
                    <div class="small">${iconClass.replace('fas fa-', '')}</div>
                </div>
            `;
            iconGrid.appendChild(iconDiv);
        });

        // Add click handlers for icon selection
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', function() {
                const selectedIcon = this.dataset.icon;
                console.log('Selected icon:', selectedIcon);
                iconClassInput.value = selectedIcon;
                updateIconPreview();
                modal.hide();
            });
        });

        console.log('Showing modal');
        modal.show();
    });

    // Icon search functionality
    document.getElementById('icon-search').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        document.querySelectorAll('.icon-option').forEach(option => {
            const iconName = option.dataset.icon.toLowerCase();
            option.parentElement.style.display = iconName.includes(searchTerm) ? 'block' : 'none';
        });
    });

    // Color picker synchronization
    if (iconColorInput) {
        iconColorInput.addEventListener('change', function() {
            if (iconColorHex) iconColorHex.value = this.value;
            updateIconPreview();
        });
    }

    if (iconColorHex) {
        iconColorHex.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                if (iconColorInput) iconColorInput.value = this.value;
                updateIconPreview();
            }
        });
    }

    function updateIconPreview() {
        const iconClass = iconClassInput.value;
        const iconColor = iconColorInput.value;

        if (iconClass) {
            previewIcon.className = iconClass + ' fa-3x';
            previewIcon.style.color = iconColor;
            iconPreview.style.display = 'block';
        } else {
            iconPreview.style.display = 'none';
        }
    }

    // Update preview when icon class changes
    iconClassInput.addEventListener('input', updateIconPreview);

    // Background type change handler
    const backgroundTypeSelect = document.getElementById('background_type');
    const backgroundColorSection = document.getElementById('background-color-section');
    const backgroundImageSection = document.getElementById('background-image-section');

    backgroundTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;

        // Hide all background sections first
        backgroundColorSection.style.display = 'none';
        backgroundImageSection.style.display = 'none';

        // Show relevant section
        if (selectedType === 'color') {
            backgroundColorSection.style.display = 'block';
        } else if (selectedType === 'image') {
            backgroundImageSection.style.display = 'block';
        }
    });

    // Trigger change events on page load to show correct sections
    backgroundTypeSelect.dispatchEvent(new Event('change'));

    // Color picker synchronization
    const backgroundColorInput = document.getElementById('background_color');
    const backgroundColorPicker = document.getElementById('background_color_picker');

    // Sync background color picker with text input
    backgroundColorPicker.addEventListener('input', function() {
        backgroundColorInput.value = this.value;
    });

    backgroundColorInput.addEventListener('input', function() {
        if (this.value.match(/^#[0-9A-F]{6}$/i)) {
            backgroundColorPicker.value = this.value;
        }
    });


    
    // Icon selection
    document.querySelectorAll('.icon-select').forEach(button => {
        button.addEventListener('click', function() {
            const iconClass = this.getAttribute('data-icon');
            document.getElementById('icon_class').value = iconClass;
            
            // Update button states
            document.querySelectorAll('.icon-select').forEach(btn => btn.classList.remove('btn-primary'));
            this.classList.add('btn-primary');
        });
    });
    
    // Trigger change event on page load to show correct section
    contentTypeSelect.dispatchEvent(new Event('change'));
    
    // Highlight current icon if it matches one of the popular icons
    const currentIcon = document.getElementById('icon_class').value;
    if (currentIcon) {
        document.querySelectorAll('.icon-select').forEach(button => {
            if (button.getAttribute('data-icon') === currentIcon) {
                button.classList.add('btn-primary');
            }
        });
    }

    // Subcontent management
    let subcontentIndex = {{ $aboutUsSection->subcontent ? count($aboutUsSection->subcontent) : 0 }};
    console.log('Starting subcontent index:', subcontentIndex);

    function addSubcontentItem(title = '', description = '', iconClass = '') {
        console.log('Adding subcontent item');
        const container = document.getElementById('subcontent-container');

        if (!container) {
            console.error('Subcontent container not found');
            return;
        }
        const itemHtml = `
            <div class="card mb-3 subcontent-item" data-index="${subcontentIndex}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Item ${subcontentIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-subcontent">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="subcontent[${subcontentIndex}][title]" value="${title}" placeholder="Enter title">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Icon</label>
                                <div class="input-group">
                                    <input type="text" class="form-control subcontent-icon-input" name="subcontent[${subcontentIndex}][icon_class]" value="${iconClass}" placeholder="e.g., fas fa-home" readonly>
                                    <button type="button" class="btn btn-outline-primary subcontent-icon-btn">
                                        <i class="fas fa-icons"></i>
                                    </button>
                                </div>
                                ${iconClass ? `<div class="mt-2 text-center subcontent-icon-preview"><i class="${iconClass} fa-2x text-primary"></i></div>` : '<div class="mt-2 text-center subcontent-icon-preview" style="display: none;"><i class="fa-2x text-primary"></i></div>'}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="subcontent[${subcontentIndex}][description]" rows="3" placeholder="Enter description">${description}</textarea>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', itemHtml);

        // Add event listeners for the new subcontent item
        const newItem = container.lastElementChild;
        const iconBtn = newItem.querySelector('.subcontent-icon-btn');
        const iconInput = newItem.querySelector('.subcontent-icon-input');
        const iconPreview = newItem.querySelector('.subcontent-icon-preview i');
        const iconPreviewContainer = newItem.querySelector('.subcontent-icon-preview');
        const removeBtn = newItem.querySelector('.remove-subcontent');

        if (iconBtn) {
            iconBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Subcontent icon button clicked');
                openSubcontentIconPicker(iconInput, iconPreview, iconPreviewContainer);
            });
        }

        if (removeBtn) {
            removeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Remove subcontent item');
                newItem.remove();
            });
        }

        subcontentIndex++;
        console.log('Subcontent item added successfully, new index:', subcontentIndex);
    }

    function openSubcontentIconPicker(inputElement, previewElement, previewContainer) {
        const modal = new bootstrap.Modal(document.getElementById('iconPickerModal'));
        const iconGrid = document.getElementById('icon-grid');

        // Clear and populate icon grid
        iconGrid.innerHTML = '';
        popularIcons.forEach(iconClass => {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'col-2 mb-3';
            iconDiv.innerHTML = `
                <div class="text-center p-2 border rounded icon-option" style="cursor: pointer;" data-icon="${iconClass}">
                    <i class="${iconClass} fa-2x mb-1"></i>
                    <div class="small">${iconClass.replace('fas fa-', '')}</div>
                </div>
            `;
            iconGrid.appendChild(iconDiv);
        });

        // Add click handlers for icon selection
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', function() {
                const selectedIcon = this.dataset.icon;
                inputElement.value = selectedIcon;
                previewElement.className = selectedIcon + ' fa-2x text-primary';
                previewContainer.style.display = 'block';
                modal.hide();
            });
        });

        modal.show();
    }

    // Add subcontent item - Use setTimeout to ensure DOM is ready
    setTimeout(function() {
        const addSubcontentBtn = document.getElementById('add-subcontent');
        console.log('Add subcontent button:', addSubcontentBtn);

        if (addSubcontentBtn) {
            addSubcontentBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Add Item button clicked');
                addSubcontentItem();
            });
            console.log('Add Item event listener attached successfully');
        } else {
            console.error('Add subcontent button not found');
        }
    }, 100);

    // Load existing subcontent items
    @if($aboutUsSection->subcontent && is_array($aboutUsSection->subcontent))
        @foreach($aboutUsSection->subcontent as $index => $item)
            addSubcontentItem('{{ $item['title'] ?? '' }}', '{{ $item['description'] ?? '' }}', '{{ $item['icon_class'] ?? '' }}');
        @endforeach
    @endif

    // Remove subcontent item
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-subcontent')) {
            e.target.closest('.subcontent-item').remove();
        }
    });

    // Load existing subcontent
    @if($aboutUsSection->subcontent)
        @foreach($aboutUsSection->subcontent as $item)
            addSubcontentItem(
                '{{ addslashes($item['title'] ?? '') }}',
                '{{ addslashes($item['description'] ?? '') }}',
                '{{ addslashes($item['icon_class'] ?? '') }}'
            );
        @endforeach
    @else
        // Add initial subcontent item if none exist
        addSubcontentItem();
    @endif
});
</script>
@endsection
