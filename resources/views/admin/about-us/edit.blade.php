@extends('admin.layouts.app')

@section('title', 'Edit About Us Section')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit About Us Section</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.about-us.update', $aboutUsSection) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="section_key" class="form-label">Section Key <span class="text-danger">*</span></label>
                                <select class="form-select @error('section_key') is-invalid @enderror" id="section_key" name="section_key" required>
                                    <option value="">Select Section Type</option>
                                    @foreach(\App\Models\AboutUsSection::getSectionTypes() as $key => $label)
                                        <option value="{{ $key }}" {{ old('section_key', $aboutUsSection->section_key) === $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('section_key')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" 
                                       value="{{ old('sort_order', $aboutUsSection->sort_order) }}" 
                                       min="1" required>
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" 
                                       value="{{ old('title', $aboutUsSection->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                       id="subtitle" name="subtitle" 
                                       value="{{ old('subtitle', $aboutUsSection->subtitle) }}">
                                @error('subtitle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="6" required>{{ old('description', $aboutUsSection->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Content Type Selection -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="content_type" class="form-label">Content Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('content_type') is-invalid @enderror" 
                                        id="content_type" name="content_type" required>
                                    <option value="">Select Content Type</option>
                                    <option value="text" {{ old('content_type', $aboutUsSection->content_type) === 'text' ? 'selected' : '' }}>Text Only</option>
                                    <option value="icon" {{ old('content_type', $aboutUsSection->content_type) === 'icon' ? 'selected' : '' }}>With Icon</option>
                                    <option value="image" {{ old('content_type', $aboutUsSection->content_type) === 'image' ? 'selected' : '' }}>With Image</option>
                                </select>
                                @error('content_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('is_active') is-invalid @enderror" id="is_active" name="is_active" required>
                                    <option value="1" {{ old('is_active', $aboutUsSection->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active', $aboutUsSection->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                </select>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Icon Selection -->
                    <div id="icon-section" class="mb-3" style="display: none;">
                        <label for="icon_class" class="form-label">Select Icon</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('icon_class') is-invalid @enderror"
                                   id="icon_class" name="icon_class" value="{{ old('icon_class', $aboutUsSection->icon_class) }}"
                                   placeholder="e.g., fas fa-home, fas fa-shield-alt" readonly>
                            <button type="button" class="btn btn-outline-secondary" id="icon-picker-btn">
                                <i class="fas fa-search"></i> Browse Icons
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Click "Browse Icons" to select from available FontAwesome icons</small>
                            <div id="icon-preview" class="mt-2" style="display: none;">
                                <strong>Preview: </strong>
                                <i id="preview-icon" class="fa-2x"></i>
                            </div>
                        </div>
                        @error('icon_class')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Icon Color Section -->
                    <div id="icon-color-section" class="mb-3" style="display: none;">
                        <label for="icon_color" class="form-label">Icon Color</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="color" class="form-control form-control-color @error('icon_color') is-invalid @enderror"
                                       id="icon_color" name="icon_color"
                                       value="{{ old('icon_color', $aboutUsSection->icon_color ?? '#cfaa13') }}">
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="icon_color_hex"
                                       value="{{ old('icon_color', $aboutUsSection->icon_color ?? '#cfaa13') }}"
                                       placeholder="#cfaa13">
                            </div>
                        </div>
                        @error('icon_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Image Upload -->
                    <div id="image-section" class="mb-3" style="display: none;">
                        <label for="image" class="form-label">Section Image</label>
                        
                        @if($aboutUsSection->hasImage())
                            <div class="mb-2">
                                <img src="{{ $aboutUsSection->image_url }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                <p class="text-muted mt-1">Current image</p>
                            </div>
                        @endif
                        
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        <small class="text-muted">Leave empty to keep current image. Supported formats: JPG, PNG, GIF (Max: 2MB)</small>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Columns Count -->
                    <div class="mb-3">
                        <label for="columns_count" class="form-label">Number of Columns</label>
                        <select class="form-select @error('columns_count') is-invalid @enderror" id="columns_count" name="columns_count">
                            <option value="1" {{ old('columns_count', $aboutUsSection->columns_count) == '1' ? 'selected' : '' }}>1 Column</option>
                            <option value="2" {{ old('columns_count', $aboutUsSection->columns_count) == '2' ? 'selected' : '' }}>2 Columns</option>
                            <option value="3" {{ old('columns_count', $aboutUsSection->columns_count) == '3' ? 'selected' : '' }}>3 Columns</option>
                            <option value="4" {{ old('columns_count', $aboutUsSection->columns_count) == '4' ? 'selected' : '' }}>4 Columns</option>
                        </select>
                        @error('columns_count')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Dynamic Subcontent Fields -->
                    <div id="subcontent-section" class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6>Subcontent Items</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-subcontent">
                                <i class="fas fa-plus me-1"></i>Add Item
                            </button>
                        </div>
                        <div id="subcontent-container">
                            <!-- Existing subcontent items will be loaded here -->
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.about-us.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.about-us.show', $aboutUsSection) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Section
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Icon Picker Modal -->
<div class="modal fade" id="iconPickerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Icon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="icon-search" placeholder="Search icons...">
                </div>
                <div id="icon-grid" class="row">
                    <!-- Icons will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    if (typeof window.initAdminCKEditor === 'function') {
        window.initAdminCKEditor('#description');
    }

    // Get DOM elements
    const contentTypeSelect = document.getElementById('content_type');
    const iconSection = document.getElementById('icon-section');
    const imageSection = document.getElementById('image-section');
    const iconColorSection = document.getElementById('icon-color-section');
    const iconClassInput = document.getElementById('icon_class');
    const iconColorInput = document.getElementById('icon_color');
    const iconColorHex = document.getElementById('icon_color_hex');
    const iconPreview = document.getElementById('icon-preview');
    const previewIcon = document.getElementById('preview-icon');

    // Content type toggle function
    function toggleContentSections() {
        const selectedType = contentTypeSelect ? contentTypeSelect.value : '';

        // Hide all sections
        if (iconSection) iconSection.style.display = 'none';
        if (imageSection) imageSection.style.display = 'none';
        if (iconColorSection) iconColorSection.style.display = 'none';

        // Show relevant sections
        if (selectedType === 'icon') {
            if (iconSection) iconSection.style.display = 'block';
            if (iconColorSection) iconColorSection.style.display = 'block';
        } else if (selectedType === 'image') {
            if (imageSection) imageSection.style.display = 'block';
        }
    }

    // Attach event listener
    if (contentTypeSelect) {
        contentTypeSelect.addEventListener('change', toggleContentSections);
        toggleContentSections(); // Initial call
    }

    // Icon color synchronization
    if (iconColorInput && iconColorHex) {
        iconColorInput.addEventListener('change', function() {
            iconColorHex.value = this.value;
            updateIconPreview();
        });

        iconColorHex.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                iconColorInput.value = this.value;
                updateIconPreview();
            }
        });
    }

    // Icon preview update
    function updateIconPreview() {
        if (iconClassInput && previewIcon && iconPreview) {
            const iconClass = iconClassInput.value;
            const iconColor = iconColorInput ? iconColorInput.value : '#000000';

            if (iconClass) {
                previewIcon.className = iconClass + ' fa-2x';
                previewIcon.style.color = iconColor;
                iconPreview.style.display = 'block';
            } else {
                iconPreview.style.display = 'none';
            }
        }
    }

    // Icon class input change
    if (iconClassInput) {
        iconClassInput.addEventListener('input', updateIconPreview);
        updateIconPreview(); // Initial call
    }

    // Icon picker functionality
    const iconPickerBtn = document.getElementById('icon-picker-btn');
    const iconPickerModal = new bootstrap.Modal(document.getElementById('iconPickerModal'));

    if (iconPickerBtn) {
        iconPickerBtn.addEventListener('click', function() {
            loadIconPicker();
            iconPickerModal.show();
        });
    }

    function loadIconPicker() {
        const iconGrid = document.getElementById('icon-grid');
        const searchInput = document.getElementById('icon-search');

        // Popular FontAwesome icons
        const icons = [
            'fas fa-home', 'fas fa-user', 'fas fa-heart', 'fas fa-star', 'fas fa-search',
            'fas fa-envelope', 'fas fa-phone', 'fas fa-map-marker-alt', 'fas fa-calendar',
            'fas fa-clock', 'fas fa-edit', 'fas fa-trash', 'fas fa-plus', 'fas fa-minus',
            'fas fa-check', 'fas fa-times', 'fas fa-arrow-left', 'fas fa-arrow-right',
            'fas fa-arrow-up', 'fas fa-arrow-down', 'fas fa-download', 'fas fa-upload',
            'fas fa-file', 'fas fa-folder', 'fas fa-image', 'fas fa-video',
            'fas fa-music', 'fas fa-headphones', 'fas fa-microphone', 'fas fa-camera',
            'fas fa-print', 'fas fa-save', 'fas fa-share', 'fas fa-link',
            'fas fa-shield-alt', 'fas fa-lock', 'fas fa-unlock', 'fas fa-key',
            'fas fa-cog', 'fas fa-wrench', 'fas fa-hammer', 'fas fa-tools',
            'fas fa-car', 'fas fa-plane', 'fas fa-train', 'fas fa-bicycle',
            'fas fa-shopping-cart', 'fas fa-credit-card', 'fas fa-money-bill',
            'fas fa-gift', 'fas fa-trophy', 'fas fa-medal', 'fas fa-award',
            'fas fa-thumbs-up', 'fas fa-thumbs-down', 'fas fa-smile', 'fas fa-frown',
            'fas fa-building', 'fas fa-hospital', 'fas fa-school', 'fas fa-university',
            'fas fa-chart-bar', 'fas fa-chart-line', 'fas fa-chart-pie', 'fas fa-table',
            'fas fa-list', 'fas fa-th', 'fas fa-th-large', 'fas fa-th-list'
        ];

        function renderIcons(iconsToShow) {
            iconGrid.innerHTML = '';
            iconsToShow.forEach(icon => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'col-2 text-center p-2';
                iconDiv.innerHTML = `
                    <div class="icon-option p-2 border rounded cursor-pointer" data-icon="${icon}" style="cursor: pointer;">
                        <i class="${icon} fa-2x mb-1"></i>
                        <small class="d-block text-muted">${icon.replace('fas fa-', '')}</small>
                    </div>
                `;
                iconGrid.appendChild(iconDiv);
            });
        }

        // Initial render
        renderIcons(icons);

        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filteredIcons = icons.filter(icon =>
                icon.toLowerCase().includes(searchTerm)
            );
            renderIcons(filteredIcons);
        });

        // Icon selection
        iconGrid.addEventListener('click', function(e) {
            const iconOption = e.target.closest('.icon-option');
            if (iconOption) {
                const selectedIcon = iconOption.dataset.icon;
                iconClassInput.value = selectedIcon;
                updateIconPreview();
                iconPickerModal.hide();
            }
        });
    }

    // Subcontent management
    let subcontentIndex = {{ $aboutUsSection->subcontent ? count($aboutUsSection->subcontent) : 0 }};

    function addSubcontentItem(title = '', description = '', iconClass = '') {
        const container = document.getElementById('subcontent-container');
        if (!container) return;

        const itemHtml = `
            <div class="subcontent-item border rounded p-3 mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Item ${subcontentIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-subcontent">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="subcontent[${subcontentIndex}][title]"
                               placeholder="Item Title" value="${title}">
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="subcontent[${subcontentIndex}][icon_class]"
                               placeholder="Icon Class (e.g., fas fa-home)" value="${iconClass}">
                    </div>
                    <div class="col-md-4">
                        <textarea class="form-control" name="subcontent[${subcontentIndex}][description]"
                                  placeholder="Item Description" rows="2">${description}</textarea>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', itemHtml);
        subcontentIndex++;
    }

    // Add subcontent button
    const addSubcontentBtn = document.getElementById('add-subcontent');
    if (addSubcontentBtn) {
        addSubcontentBtn.addEventListener('click', function() {
            addSubcontentItem();
        });
    }

    // Remove subcontent item
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-subcontent')) {
            e.target.closest('.subcontent-item').remove();
        }
    });

    // Load existing subcontent
    @if($aboutUsSection->subcontent)
        @foreach($aboutUsSection->subcontent as $item)
            addSubcontentItem(
                '{{ addslashes($item['title'] ?? '') }}',
                '{{ addslashes($item['description'] ?? '') }}',
                '{{ addslashes($item['icon_class'] ?? '') }}'
            );
        @endforeach
    @else
        addSubcontentItem(); // Add one empty item
    @endif
});
</script>
@endsection
