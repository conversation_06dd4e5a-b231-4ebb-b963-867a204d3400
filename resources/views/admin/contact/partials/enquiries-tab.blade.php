<!-- Search and Filter Form -->
<div class="p-3 border-bottom">
    <form method="GET" action="{{ route('admin.contact.index') }}" class="row g-3">
        <input type="hidden" name="tab" value="enquiries">
        
        <div class="col-md-4">
            <input type="text" 
                   class="form-control" 
                   name="enquiry_search" 
                   placeholder="Search enquiries..." 
                   value="{{ request('enquiry_search') }}">
        </div>
        
        <div class="col-md-2">
            <select name="enquiry_status" class="form-select">
                <option value="">All Status</option>
                <option value="new" {{ request('enquiry_status') === 'new' ? 'selected' : '' }}>New</option>
                <option value="contacted" {{ request('enquiry_status') === 'contacted' ? 'selected' : '' }}>Contacted</option>
                <option value="in_progress" {{ request('enquiry_status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                <option value="resolved" {{ request('enquiry_status') === 'resolved' ? 'selected' : '' }}>Resolved</option>
                <option value="closed" {{ request('enquiry_status') === 'closed' ? 'selected' : '' }}>Closed</option>
            </select>
        </div>
        
        <div class="col-md-2">
            <select name="enquiry_type" class="form-select">
                <option value="">All Types</option>
                <option value="general" {{ request('enquiry_type') === 'general' ? 'selected' : '' }}>General</option>
                <option value="property" {{ request('enquiry_type') === 'property' ? 'selected' : '' }}>Property</option>
                <option value="investment" {{ request('enquiry_type') === 'investment' ? 'selected' : '' }}>Investment</option>
            </select>
        </div>
        
        <div class="col-md-4">
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-admin-primary">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{{ route('admin.contact.index', ['tab' => 'enquiries']) }}" class="btn btn-admin-outline">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </div>
    </form>
</div>

<!-- Enquiries Table -->
<div class="table-responsive">
    <table class="table admin-table mb-0">
        <thead>
            <tr>
                <th width="50">#</th>
                <th>Name & Contact</th>
                <th>Subject</th>
                <th>Type</th>
                <th>Status</th>
                <th>Date</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($enquiries as $index => $enquiry)
                <tr>
                    <td>{{ $enquiries->firstItem() + $index }}</td>
                    <td>
                        <div>
                            <strong>{{ $enquiry->name }}</strong>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>{{ $enquiry->email }}
                                <br>
                                <i class="fas fa-phone me-1"></i>{{ $enquiry->phone }}
                            </small>
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>{{ $enquiry->subject }}</strong>
                            <br>
                            <small class="text-muted">{{ Str::limit($enquiry->message, 60) }}</small>
                        </div>
                    </td>
                    <td>{!! $enquiry->enquiry_type_badge !!}</td>
                    <td>{!! $enquiry->status_badge !!}</td>
                    <td>
                        <small>{{ $enquiry->created_at->format('M d, Y') }}</small>
                        <br>
                        <small class="text-muted">{{ $enquiry->created_at->format('g:i A') }}</small>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <a href="{{ route('admin.contact.enquiry.show', $enquiry) }}" 
                               class="btn btn-sm btn-admin-outline" 
                               title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-admin-primary dropdown-toggle" 
                                        type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form action="{{ route('admin.contact.enquiry.update-status', $enquiry) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="contacted">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-phone me-2"></i>Mark as Contacted
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form action="{{ route('admin.contact.enquiry.update-status', $enquiry) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="resolved">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-check me-2"></i>Mark as Resolved
                                            </button>
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="{{ route('admin.contact.enquiry.delete', $enquiry) }}" 
                                              method="POST" 
                                              class="d-inline"
                                              onsubmit="return confirm('Are you sure you want to delete this enquiry?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No enquiries found.</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Pagination -->
@if($enquiries->hasPages())
    <div class="p-3 border-top">
        {{ $enquiries->appends(request()->query())->links() }}
    </div>
@endif
