<!-- Search and Filter Form -->
<div class="p-3 border-bottom">
    <form method="GET" action="{{ route('admin.contact.index') }}" class="row g-3">
        <input type="hidden" name="tab" value="visits">
        
        <div class="col-md-4">
            <input type="text" 
                   class="form-control" 
                   name="visit_search" 
                   placeholder="Search visits..." 
                   value="{{ request('visit_search') }}">
        </div>
        
        <div class="col-md-2">
            <select name="visit_status" class="form-select">
                <option value="">All Status</option>
                <option value="pending" {{ request('visit_status') === 'pending' ? 'selected' : '' }}>Pending</option>
                <option value="confirmed" {{ request('visit_status') === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                <option value="completed" {{ request('visit_status') === 'completed' ? 'selected' : '' }}>Completed</option>
                <option value="cancelled" {{ request('visit_status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                <option value="rescheduled" {{ request('visit_status') === 'rescheduled' ? 'selected' : '' }}>Rescheduled</option>
            </select>
        </div>
        
        <div class="col-md-2">
            <select name="visit_type" class="form-select">
                <option value="">All Types</option>
                <option value="property_viewing" {{ request('visit_type') === 'property_viewing' ? 'selected' : '' }}>Property Viewing</option>
                <option value="consultation" {{ request('visit_type') === 'consultation' ? 'selected' : '' }}>Consultation</option>
                <option value="site_visit" {{ request('visit_type') === 'site_visit' ? 'selected' : '' }}>Site Visit</option>
            </select>
        </div>
        
        <div class="col-md-4">
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-admin-primary">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{{ route('admin.contact.index', ['tab' => 'visits']) }}" class="btn btn-admin-outline">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </div>
    </form>
</div>

<!-- Visits Table -->
<div class="table-responsive">
    <table class="table admin-table mb-0">
        <thead>
            <tr>
                <th width="50">#</th>
                <th>Name & Contact</th>
                <th>Property Details</th>
                <th>Preferred Date</th>
                <th>Type</th>
                <th>Status</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($visits as $index => $visit)
                <tr>
                    <td>{{ $visits->firstItem() + $index }}</td>
                    <td>
                        <div>
                            <strong>{{ $visit->name }}</strong>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>{{ $visit->email }}
                                <br>
                                <i class="fas fa-phone me-1"></i>{{ $visit->phone }}
                            </small>
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>{{ $visit->property_type }}</strong>
                            @if($visit->location_preference)
                                <br><small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ $visit->location_preference }}
                                </small>
                            @endif
                            @if($visit->budget_range)
                                <br><small class="text-muted">
                                    <i class="fas fa-rupee-sign me-1"></i>{{ $visit->budget_range }}
                                </small>
                            @endif
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>{{ $visit->preferred_date->format('M d, Y') }}</strong>
                            <br>
                            <small class="text-muted">{{ $visit->preferred_time->format('g:i A') }}</small>
                        </div>
                    </td>
                    <td>{!! $visit->visit_type_badge !!}</td>
                    <td>{!! $visit->status_badge !!}</td>
                    <td>
                        <div class="d-flex gap-1">
                            <a href="{{ route('admin.contact.visit.show', $visit) }}" 
                               class="btn btn-sm btn-admin-outline" 
                               title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-admin-primary dropdown-toggle" 
                                        type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form action="{{ route('admin.contact.visit.update-status', $visit) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="confirmed">
                                            <input type="hidden" name="scheduled_date" value="{{ $visit->preferred_date->format('Y-m-d') }}">
                                            <input type="hidden" name="scheduled_time" value="{{ $visit->preferred_time->format('H:i') }}">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-check me-2"></i>Confirm Visit
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form action="{{ route('admin.contact.visit.update-status', $visit) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-flag-checkered me-2"></i>Mark as Completed
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form action="{{ route('admin.contact.visit.update-status', $visit) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="cancelled">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-times me-2"></i>Cancel Visit
                                            </button>
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="{{ route('admin.contact.visit.delete', $visit) }}" 
                                              method="POST" 
                                              class="d-inline"
                                              onsubmit="return confirm('Are you sure you want to delete this visit schedule?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>No visit schedules found.</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Pagination -->
@if($visits->hasPages())
    <div class="p-3 border-top">
        {{ $visits->appends(request()->query())->links() }}
    </div>
@endif
