@extends('admin.layouts.app')

@section('title', 'Create Contact Section')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.contact.index') }}" class="breadcrumb-item">Contact</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Create</span>
    </div>
    <h1 class="page-title">Create Contact Section</h1>
    <p class="page-subtitle">Add a new contact section to your contact page</p>
    <div class="page-actions">
        <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Contact
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        New Contact Section
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.contact.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title *</label>
                                    <input type="text" 
                                           class="form-control @error('title') is-invalid @enderror" 
                                           id="title" 
                                           name="title" 
                                           value="{{ old('title') }}" 
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', 1) }}" 
                                           min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="4" 
                                      required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="contact_info" class="form-label">Contact Information</label>
                            <textarea class="form-control @error('contact_info') is-invalid @enderror" 
                                      id="contact_info" 
                                      name="contact_info" 
                                      rows="3" 
                                      placeholder="Additional contact details, phone numbers, emails, etc.">{{ old('contact_info') }}</textarea>
                            @error('contact_info')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="background_type" class="form-label">Background Type</label>
                            <select class="form-select @error('background_type') is-invalid @enderror" 
                                    id="background_type" 
                                    name="background_type" 
                                    onchange="toggleBackgroundOptions()">
                                <option value="none" {{ old('background_type') == 'none' ? 'selected' : '' }}>None</option>
                                <option value="color" {{ old('background_type') == 'color' ? 'selected' : '' }}>Color</option>
                                <option value="image" {{ old('background_type') == 'image' ? 'selected' : '' }}>Image</option>
                            </select>
                            @error('background_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="background_color_field" style="display: {{ old('background_type') == 'color' ? 'block' : 'none' }};">
                            <label for="background_color" class="form-label">Background Color</label>
                            <input type="color" 
                                   class="form-control form-control-color @error('background_color') is-invalid @enderror" 
                                   id="background_color" 
                                   name="background_color" 
                                   value="{{ old('background_color', '#ffffff') }}">
                            @error('background_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="background_image_field" style="display: {{ old('background_type') == 'image' ? 'block' : 'none' }};">
                            <label for="background_image" class="form-label">Background Image</label>
                            <input type="file" 
                                   class="form-control @error('background_image') is-invalid @enderror" 
                                   id="background_image" 
                                   name="background_image" 
                                   accept="image/*">
                            @error('background_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="icon_color" class="form-label">Icon Color</label>
                            <input type="color" 
                                   class="form-control form-control-color @error('icon_color') is-invalid @enderror" 
                                   id="icon_color" 
                                   name="icon_color" 
                                   value="{{ old('icon_color', '#cfaa13') }}">
                            @error('icon_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Create Contact Section
                            </button>
                            <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="tip-item">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>Title:</strong> Keep it short and descriptive (e.g., "Get in Touch", "Office Location")
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>Description:</strong> Provide clear information about this contact method or location
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>Contact Info:</strong> Add specific details like phone numbers, emails, or addresses
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>Background:</strong> Use colors or images to make sections visually distinct
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>Sort Order:</strong> Lower numbers appear first on the contact page
                    </div>
                </div>
            </div>

            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Color Suggestions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="color-suggestions">
                        <div class="color-item" onclick="setColor('#cfaa13')">
                            <div class="color-preview" style="background: #cfaa13;"></div>
                            <span>Primary Gold</span>
                        </div>
                        <div class="color-item" onclick="setColor('#3b82f6')">
                            <div class="color-preview" style="background: #3b82f6;"></div>
                            <span>Blue</span>
                        </div>
                        <div class="color-item" onclick="setColor('#10b981')">
                            <div class="color-preview" style="background: #10b981;"></div>
                            <span>Green</span>
                        </div>
                        <div class="color-item" onclick="setColor('#f59e0b')">
                            <div class="color-preview" style="background: #f59e0b;"></div>
                            <span>Orange</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function toggleBackgroundOptions() {
    const backgroundType = document.getElementById('background_type').value;
    const colorField = document.getElementById('background_color_field');
    const imageField = document.getElementById('background_image_field');
    
    colorField.style.display = backgroundType === 'color' ? 'block' : 'none';
    imageField.style.display = backgroundType === 'image' ? 'block' : 'none';
}

function setColor(color) {
    document.getElementById('icon_color').value = color;
}
</script>
@endpush

@push('styles')
<style>
.tip-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.tip-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.color-suggestions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.color-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

.color-item:hover {
    background: #f9fafb;
    border-color: #cfaa13;
}

.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid #e5e7eb;
}

.color-item span {
    font-size: 0.875rem;
    color: #6b7280;
}
</style>
@endpush
