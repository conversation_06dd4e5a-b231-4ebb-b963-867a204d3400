@extends('admin.layouts.app')

@section('title', 'Edit Contact Section')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.contact.index') }}" class="breadcrumb-item">Contact</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Edit</span>
    </div>
    <h1 class="page-title">Edit Contact Section</h1>
    <p class="page-subtitle">Update contact section information</p>
    <div class="page-actions">
        <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Contact
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Edit Contact Section
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.contact.update', $contact) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title *</label>
                                    <input type="text" 
                                           class="form-control @error('title') is-invalid @enderror" 
                                           id="title" 
                                           name="title" 
                                           value="{{ old('title', $contact->title) }}" 
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', $contact->sort_order) }}" 
                                           min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="4" 
                                      required>{{ old('description', $contact->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="contact_info" class="form-label">Contact Information</label>
                            <textarea class="form-control @error('contact_info') is-invalid @enderror" 
                                      id="contact_info" 
                                      name="contact_info" 
                                      rows="3" 
                                      placeholder="Additional contact details, phone numbers, emails, etc.">{{ old('contact_info', $contact->contact_info) }}</textarea>
                            @error('contact_info')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="background_type" class="form-label">Background Type</label>
                            <select class="form-select @error('background_type') is-invalid @enderror" 
                                    id="background_type" 
                                    name="background_type" 
                                    onchange="toggleBackgroundOptions()">
                                <option value="none" {{ old('background_type', $contact->background_type) == 'none' ? 'selected' : '' }}>None</option>
                                <option value="color" {{ old('background_type', $contact->background_type) == 'color' ? 'selected' : '' }}>Color</option>
                                <option value="image" {{ old('background_type', $contact->background_type) == 'image' ? 'selected' : '' }}>Image</option>
                            </select>
                            @error('background_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="background_color_field" style="display: {{ old('background_type', $contact->background_type) == 'color' ? 'block' : 'none' }};">
                            <label for="background_color" class="form-label">Background Color</label>
                            <input type="color" 
                                   class="form-control form-control-color @error('background_color') is-invalid @enderror" 
                                   id="background_color" 
                                   name="background_color" 
                                   value="{{ old('background_color', $contact->background_color ?? '#ffffff') }}">
                            @error('background_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="background_image_field" style="display: {{ old('background_type', $contact->background_type) == 'image' ? 'block' : 'none' }};">
                            <label for="background_image" class="form-label">Background Image</label>
                            @if($contact->background_image)
                                <div class="current-image mb-2">
                                    <img src="{{ Storage::url($contact->background_image) }}" 
                                         alt="Current background" 
                                         class="img-thumbnail" 
                                         style="max-height: 100px;">
                                    <small class="text-muted d-block">Current background image</small>
                                </div>
                            @endif
                            <input type="file" 
                                   class="form-control @error('background_image') is-invalid @enderror" 
                                   id="background_image" 
                                   name="background_image" 
                                   accept="image/*">
                            <small class="form-text text-muted">Leave empty to keep current image</small>
                            @error('background_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="icon_color" class="form-label">Icon Color</label>
                            <input type="color" 
                                   class="form-control form-control-color @error('icon_color') is-invalid @enderror" 
                                   id="icon_color" 
                                   name="icon_color" 
                                   value="{{ old('icon_color', $contact->icon_color ?? '#cfaa13') }}">
                            @error('icon_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', $contact->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Update Contact Section
                            </button>
                            <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Section Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Created:</strong>
                        <span>{{ $contact->created_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Last Updated:</strong>
                        <span>{{ $contact->updated_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Status:</strong>
                        @if($contact->is_active)
                            <span class="badge badge-success">Active</span>
                        @else
                            <span class="badge badge-danger">Inactive</span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div class="contact-preview" 
                         style="background: {{ $contact->background_type == 'color' ? $contact->background_color : 'transparent' }}; 
                                {{ $contact->background_type == 'image' && $contact->background_image ? 'background-image: url(' . Storage::url($contact->background_image) . '); background-size: cover; background-position: center;' : '' }}">
                        <h6 style="color: {{ $contact->icon_color ?? '#cfaa13' }};">{{ $contact->title }}</h6>
                        <p class="small">{{ Str::limit($contact->description, 100) }}</p>
                        @if($contact->contact_info)
                            <small class="text-muted">{{ Str::limit($contact->contact_info, 50) }}</small>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function toggleBackgroundOptions() {
    const backgroundType = document.getElementById('background_type').value;
    const colorField = document.getElementById('background_color_field');
    const imageField = document.getElementById('background_image_field');
    
    colorField.style.display = backgroundType === 'color' ? 'block' : 'none';
    imageField.style.display = backgroundType === 'image' ? 'block' : 'none';
}
</script>
@endpush
