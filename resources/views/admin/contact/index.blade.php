@extends('admin.layouts.app')

@section('title', 'Contact Sections')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Contact</span>
    </div>
    <h1 class="page-title">Contact Sections</h1>
    <p class="page-subtitle">Manage contact page sections and information</p>
    <div class="page-actions">
        <a href="{{ route('admin.contact.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Section
        </a>
    </div>
@endsection

@section('content')
    <div class="admin-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-envelope me-2"></i>
                Contact Sections
                <span class="badge badge-info ms-2">{{ $contactSections->count() }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            @if($contactSections->count() > 0)
                <div class="table-responsive">
                    <table class="admin-table table">
                        <thead>
                            <tr>
                                <th width="60">#</th>
                                <th>Title</th>
                                <th>Description</th>
                                <th>Background</th>
                                <th>Icon Color</th>
                                <th>Sort Order</th>
                                <th>Status</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($contactSections as $index => $section)
                                <tr>
                                    <td>
                                        <span class="text-muted fw-medium">{{ $index + 1 }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ $section->title }}</strong>
                                    </td>
                                    <td>
                                        <div style="max-width: 300px;">
                                            {{ Str::limit(strip_tags($section->description), 100) }}
                                        </div>
                                    </td>
                                    <td>
                                        @if($section->background_type === 'color')
                                            <div class="d-flex align-items-center">
                                                <div class="color-preview me-2" 
                                                     style="width: 20px; height: 20px; background-color: {{ $section->background_color }}; border: 1px solid #ddd; border-radius: 3px;"></div>
                                                <span class="badge badge-info">Color</span>
                                            </div>
                                        @elseif($section->background_type === 'image')
                                            <span class="badge badge-success">Image</span>
                                        @else
                                            <span class="badge badge-secondary">None</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($section->icon_color)
                                            <div class="d-flex align-items-center">
                                                <div class="color-preview me-2" 
                                                     style="width: 20px; height: 20px; background-color: {{ $section->icon_color }}; border: 1px solid #ddd; border-radius: 3px;"></div>
                                                <small>{{ $section->icon_color }}</small>
                                            </div>
                                        @else
                                            <span class="text-muted">Default</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $section->sort_order }}</span>
                                    </td>
                                    <td>
                                        @if($section->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.contact.show', $section) }}" 
                                               class="btn-admin btn-admin-outline btn-admin-sm" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.contact.edit', $section) }}" 
                                               class="btn-admin btn-admin-primary btn-admin-sm" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.contact.destroy', $section) }}" 
                                                  method="POST" 
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this contact section?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-admin btn-admin-danger btn-admin-sm" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4 class="empty-title">No Contact Sections Found</h4>
                    <p class="empty-subtitle">Start by creating your first contact section to organize your contact page content.</p>
                    <a href="{{ route('admin.contact.create') }}" class="btn-admin btn-admin-primary">
                        <i class="fas fa-plus me-2"></i>Create First Section
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
