@extends('admin.layouts.app')

@section('title', 'Contact Management')

@section('content')
<div class="admin-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Contact Management</h1>
            <p class="text-muted">Manage contact enquiries and visit schedules</p>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ $stats['total_enquiries'] }}</h4>
                    <small class="text-muted">Total Enquiries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ $stats['new_enquiries'] }}</h4>
                    <small class="text-muted">New Enquiries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ $stats['pending_visits'] }}</h4>
                    <small class="text-muted">Pending Visits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ $stats['confirmed_visits'] }}</h4>
                    <small class="text-muted">Confirmed Visits</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="admin-card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link {{ $tab === 'enquiries' ? 'active' : '' }}"
                       href="{{ route('admin.contact.index', ['tab' => 'enquiries']) }}">
                        <i class="fas fa-envelope me-2"></i>Contact Enquiries
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link {{ $tab === 'visits' ? 'active' : '' }}"
                       href="{{ route('admin.contact.index', ['tab' => 'visits']) }}">
                        <i class="fas fa-calendar-alt me-2"></i>Visit Schedules
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body p-0">
            @if($tab === 'enquiries')
                @include('admin.contact.partials.enquiries-tab')
            @else
                @include('admin.contact.partials.visits-tab')
            @endif
        </div>
    </div>
</div>
@endsection