@extends('admin.layouts.app')

@section('title', 'Contact Section Details')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.contact.index') }}" class="breadcrumb-item">Contact</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">{{ $contact->title }}</span>
    </div>
    <h1 class="page-title">{{ $contact->title }}</h1>
    <p class="page-subtitle">Contact section details and information</p>
    <div class="page-actions">
        <a href="{{ route('admin.contact.edit', $contact) }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit Section
        </a>
        <a href="{{ route('admin.contact.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Contact
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <!-- Contact Section Preview -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        Section Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="contact-section-preview p-4 rounded" 
                         style="background: {{ $contact->background_type == 'color' ? $contact->background_color : '#f8f9fa' }}; 
                                {{ $contact->background_type == 'image' && $contact->background_image ? 'background-image: url(' . Storage::url($contact->background_image) . '); background-size: cover; background-position: center;' : '' }}
                                {{ $contact->background_type == 'image' ? 'color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);' : '' }}">
                        
                        @if($contact->background_type == 'image' && $contact->background_image)
                            <div class="overlay" style="background: rgba(0,0,0,0.3); padding: 2rem; border-radius: 8px;">
                        @endif
                        
                        <div class="text-center mb-3">
                            <i class="fas fa-envelope fa-3x mb-3" style="color: {{ $contact->icon_color ?? '#cfaa13' }};"></i>
                            <h3 class="mb-3">{{ $contact->title }}</h3>
                        </div>
                        
                        <div class="description mb-3">
                            {!! nl2br(e($contact->description)) !!}
                        </div>
                        
                        <div class="contact-details mt-3">
                            @if($contact->phone)
                                <div class="contact-item mb-2">
                                    <i class="fas fa-phone text-primary me-2"></i>
                                    <strong>Phone:</strong> {{ $contact->phone }}
                                </div>
                            @endif

                            @if($contact->email)
                                <div class="contact-item mb-2">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    <strong>Email:</strong> {{ $contact->email }}
                                </div>
                            @endif

                            @if($contact->address)
                                <div class="contact-item mb-2">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <strong>Address:</strong> {{ $contact->address }}
                                </div>
                            @endif

                            @if($contact->working_hours)
                                <div class="contact-item mb-2">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <strong>Working Hours:</strong> {{ $contact->working_hours }}
                                </div>
                            @endif

                            @if($contact->website)
                                <div class="contact-item mb-2">
                                    <i class="fas fa-globe text-primary me-2"></i>
                                    <strong>Website:</strong> <a href="{{ $contact->website }}" target="_blank">{{ $contact->website }}</a>
                                </div>
                            @endif
                        </div>
                        
                        @if($contact->background_type == 'image' && $contact->background_image)
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Section Content -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Section Content
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">Title:</label>
                                <div class="info-value">{{ $contact->title }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">Sort Order:</label>
                                <div class="info-value">
                                    <span class="badge badge-secondary">{{ $contact->sort_order }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-group">
                        <label class="info-label">Description:</label>
                        <div class="info-value">
                            <div class="border rounded p-3 bg-light">
                                {!! nl2br(e($contact->description)) !!}
                            </div>
                        </div>
                    </div>

                    @if($contact->phone || $contact->email || $contact->address || $contact->working_hours || $contact->website)
                        <div class="info-group">
                            <label class="info-label">Contact Details:</label>
                            <div class="info-value">
                                <div class="border rounded p-3 bg-light">
                                    @if($contact->phone)
                                        <div class="mb-2">
                                            <i class="fas fa-phone text-primary me-2"></i>
                                            <strong>Phone:</strong> {{ $contact->phone }}
                                        </div>
                                    @endif

                                    @if($contact->email)
                                        <div class="mb-2">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <strong>Email:</strong> {{ $contact->email }}
                                        </div>
                                    @endif

                                    @if($contact->address)
                                        <div class="mb-2">
                                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                            <strong>Address:</strong> {{ $contact->address }}
                                        </div>
                                    @endif

                                    @if($contact->working_hours)
                                        <div class="mb-2">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            <strong>Working Hours:</strong> {{ $contact->working_hours }}
                                        </div>
                                    @endif

                                    @if($contact->website)
                                        <div class="mb-2">
                                            <i class="fas fa-globe text-primary me-2"></i>
                                            <strong>Website:</strong> <a href="{{ $contact->website }}" target="_blank">{{ $contact->website }}</a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Section Status -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Section Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Status:</strong>
                        @if($contact->is_active)
                            <span class="badge badge-success">Active</span>
                        @else
                            <span class="badge badge-danger">Inactive</span>
                        @endif
                    </div>
                    <div class="info-item">
                        <strong>Sort Order:</strong>
                        <span class="badge badge-secondary">{{ $contact->sort_order }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Created:</strong>
                        <span>{{ $contact->created_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Last Updated:</strong>
                        <span>{{ $contact->updated_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                </div>
            </div>

            <!-- Background Settings -->
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Background Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Background Type:</strong>
                        <span class="badge badge-info">{{ ucfirst($contact->background_type) }}</span>
                    </div>
                    
                    @if($contact->background_type == 'color' && $contact->background_color)
                        <div class="info-item">
                            <strong>Background Color:</strong>
                            <div class="d-flex align-items-center">
                                <div class="color-preview me-2" 
                                     style="width: 20px; height: 20px; background: {{ $contact->background_color }}; border: 1px solid #ddd; border-radius: 3px;"></div>
                                <code>{{ $contact->background_color }}</code>
                            </div>
                        </div>
                    @endif
                    
                    @if($contact->background_type == 'image' && $contact->background_image)
                        <div class="info-item">
                            <strong>Background Image:</strong>
                            <div class="mt-2">
                                <img src="{{ Storage::url($contact->background_image) }}" 
                                     alt="Background image" 
                                     class="img-thumbnail" 
                                     style="max-height: 100px;">
                            </div>
                        </div>
                    @endif
                    
                    @if($contact->icon_color)
                        <div class="info-item">
                            <strong>Icon Color:</strong>
                            <div class="d-flex align-items-center">
                                <div class="color-preview me-2" 
                                     style="width: 20px; height: 20px; background: {{ $contact->icon_color }}; border: 1px solid #ddd; border-radius: 3px;"></div>
                                <code>{{ $contact->icon_color }}</code>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.contact.edit', $contact) }}" class="btn-admin btn-admin-primary btn-admin-sm">
                            <i class="fas fa-edit me-2"></i>Edit Section
                        </a>
                        
                        @if($contact->is_active)
                            <form action="{{ route('admin.contact.update', $contact) }}" method="POST" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_active" value="0">
                                <input type="hidden" name="title" value="{{ $contact->title }}">
                                <input type="hidden" name="description" value="{{ $contact->description }}">
                                <input type="hidden" name="background_type" value="{{ $contact->background_type }}">
                                <button type="submit" class="btn-admin btn-admin-warning btn-admin-sm w-100">
                                    <i class="fas fa-eye-slash me-2"></i>Deactivate
                                </button>
                            </form>
                        @else
                            <form action="{{ route('admin.contact.update', $contact) }}" method="POST" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_active" value="1">
                                <input type="hidden" name="title" value="{{ $contact->title }}">
                                <input type="hidden" name="description" value="{{ $contact->description }}">
                                <input type="hidden" name="background_type" value="{{ $contact->background_type }}">
                                <button type="submit" class="btn-admin btn-admin-success btn-admin-sm w-100">
                                    <i class="fas fa-eye me-2"></i>Activate
                                </button>
                            </form>
                        @endif
                        
                        <form action="{{ route('admin.contact.destroy', $contact) }}" 
                              method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this contact section?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-admin btn-admin-danger btn-admin-sm w-100">
                                <i class="fas fa-trash me-2"></i>Delete Section
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
.contact-section-preview {
    min-height: 200px;
    position: relative;
}

.info-group {
    margin-bottom: 1.5rem;
}

.info-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.info-value {
    color: #6b7280;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
    border-bottom: none;
}

.color-preview {
    display: inline-block;
    vertical-align: middle;
}
</style>
@endpush
