@extends('admin.layouts.app')

@section('title', 'Add New Service')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.services.index') }}" class="breadcrumb-item">Services</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Add New</span>
    </div>
    <h1 class="page-title">Add New Service</h1>
    <p class="page-subtitle">Create a new service with background and styling options</p>
@endsection

@section('content')
<form action="{{ route('admin.services.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="title" class="form-label">Service Title *</label>
                                <input type="text"
                                       name="title"
                                       id="title"
                                       class="form-control @error('title') is-invalid @enderror"
                                       value="{{ old('title') }}"
                                       required
                                       placeholder="Enter service title">
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="category" class="form-label">Category</label>
                                <select name="category"
                                        id="category"
                                        class="form-select @error('category') is-invalid @enderror">
                                    <option value="">Select Category</option>
                                    @foreach(\App\Models\Service::getServiceCategories() as $key => $label)
                                        <option value="{{ $key }}" {{ old('category') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_type" class="form-label">Service Type</label>
                                <select name="service_type"
                                        id="service_type"
                                        class="form-select @error('service_type') is-invalid @enderror">
                                    @foreach(\App\Models\Service::getServiceTypes() as $key => $label)
                                        <option value="{{ $key }}" {{ old('service_type', 'primary') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('service_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="icon_class" class="form-label">Icon Class</label>
                                <input type="text"
                                       name="icon_class"
                                       id="icon_class"
                                       class="form-control @error('icon_class') is-invalid @enderror"
                                       value="{{ old('icon_class') }}"
                                       placeholder="e.g., fas fa-home">
                                <div class="form-text">Font Awesome icon class (optional)</div>
                                @error('icon_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Service Description *</label>
                        <textarea name="description" 
                                  id="description"
                                  class="form-control @error('description') is-invalid @enderror" 
                                  rows="6"
                                  required
                                  placeholder="Enter detailed service description">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Background & Icon Settings -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Background & Icon Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="background_type" class="form-label">Background Type *</label>
                                <select name="background_type" 
                                        id="background_type" 
                                        class="form-control @error('background_type') is-invalid @enderror" 
                                        required>
                                    <option value="none" {{ old('background_type', 'none') == 'none' ? 'selected' : '' }}>None</option>
                                    <option value="color" {{ old('background_type') == 'color' ? 'selected' : '' }}>Color</option>
                                    <option value="image" {{ old('background_type') == 'image' ? 'selected' : '' }}>Image</option>
                                </select>
                                @error('background_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="icon_color" class="form-label">Icon Color</label>
                                <div class="input-group">
                                    <input type="text" 
                                           name="icon_color" 
                                           id="icon_color"
                                           class="form-control @error('icon_color') is-invalid @enderror" 
                                           value="{{ old('icon_color', '#ffffff') }}" 
                                           placeholder="#ffffff">
                                    <input type="color" 
                                           id="icon_color_picker" 
                                           class="form-control form-control-color" 
                                           value="{{ old('icon_color', '#ffffff') }}" 
                                           title="Choose icon color">
                                </div>
                                @error('icon_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Color for icons in this service section</small>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="background_color_section" style="display: none;">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="background_color" class="form-label">Background Color</label>
                                <div class="input-group">
                                    <input type="text" 
                                           name="background_color" 
                                           id="background_color"
                                           class="form-control @error('background_color') is-invalid @enderror" 
                                           value="{{ old('background_color') }}" 
                                           placeholder="#ffffff">
                                    <input type="color" 
                                           id="background_color_picker" 
                                           class="form-control form-control-color" 
                                           value="{{ old('background_color', '#ffffff') }}" 
                                           title="Choose background color">
                                </div>
                                @error('background_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row" id="background_image_section" style="display: none;">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="background_image" class="form-label">Background Image</label>
                                <input type="file" 
                                       name="background_image" 
                                       id="background_image"
                                       class="form-control @error('background_image') is-invalid @enderror" 
                                       accept="image/*">
                                @error('background_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Upload background image for this service section</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Actions -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Actions
                    </h5>
                </div>
                <div class="card-body d-grid gap-2">
                    <button type="submit" class="btn-admin btn-admin-primary">
                        <i class="fas fa-save me-2"></i> Create Service
                    </button>
                    <a href="{{ route('admin.services.index') }}" class="btn-admin btn-admin-outline">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Background type handling
    const backgroundType = document.getElementById('background_type');
    const backgroundColorSection = document.getElementById('background_color_section');
    const backgroundImageSection = document.getElementById('background_image_section');

    function toggleBackgroundSections() {
        const selectedType = backgroundType.value;
        
        backgroundColorSection.style.display = selectedType === 'color' ? 'block' : 'none';
        backgroundImageSection.style.display = selectedType === 'image' ? 'block' : 'none';
    }

    backgroundType.addEventListener('change', toggleBackgroundSections);
    toggleBackgroundSections(); // Initialize on page load

    // Color picker synchronization for background color
    const backgroundColorInput = document.getElementById('background_color');
    const backgroundColorPicker = document.getElementById('background_color_picker');

    if (backgroundColorInput && backgroundColorPicker) {
        backgroundColorInput.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                backgroundColorPicker.value = this.value;
            }
        });

        backgroundColorPicker.addEventListener('input', function() {
            backgroundColorInput.value = this.value;
        });
    }

    // Color picker synchronization for icon color
    const iconColorInput = document.getElementById('icon_color');
    const iconColorPicker = document.getElementById('icon_color_picker');

    if (iconColorInput && iconColorPicker) {
        iconColorInput.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                iconColorPicker.value = this.value;
            }
        });

        iconColorPicker.addEventListener('input', function() {
            iconColorInput.value = this.value;
        });
    }
});
</script>
@endpush
