@extends('admin.layouts.app')

@section('title', 'Service Details')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.services.index') }}" class="breadcrumb-item">Services</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">{{ $service->title }}</span>
    </div>
    <h1 class="page-title">Service Details</h1>
    <p class="page-subtitle">View service information and styling settings</p>
    <div class="page-actions">
        <a href="{{ route('admin.services.edit', $service) }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit Service
        </a>
        <a href="{{ route('admin.services.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-arrow-left me-2"></i>Back to Services
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label fw-bold">Service Title</label>
                                <p class="form-control-plaintext">{{ $service->title }}</p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label fw-bold">Service Description</label>
                                <div class="form-control-plaintext">
                                    {!! nl2br(e($service->description)) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Background & Icon Settings -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>
                        Background & Icon Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-bold">Background Type</label>
                                <p class="form-control-plaintext">
                                    @if($service->background_type === 'color')
                                        <span class="badge badge-info">Color Background</span>
                                    @elseif($service->background_type === 'image')
                                        <span class="badge badge-success">Image Background</span>
                                    @else
                                        <span class="badge badge-secondary">No Background</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-bold">Icon Color</label>
                                <div class="d-flex align-items-center">
                                    @if($service->icon_color)
                                        <div class="color-preview me-2" 
                                             style="width: 30px; height: 30px; background-color: {{ $service->icon_color }}; border: 1px solid #ddd; border-radius: 5px;"></div>
                                        <span class="form-control-plaintext">{{ $service->icon_color }}</span>
                                    @else
                                        <span class="form-control-plaintext text-muted">Default Color</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($service->background_type === 'color' && $service->background_color)
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label fw-bold">Background Color</label>
                                    <div class="d-flex align-items-center">
                                        <div class="color-preview me-2" 
                                             style="width: 30px; height: 30px; background-color: {{ $service->background_color }}; border: 1px solid #ddd; border-radius: 5px;"></div>
                                        <span class="form-control-plaintext">{{ $service->background_color }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($service->background_type === 'image' && $service->background_image)
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label fw-bold">Background Image</label>
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $service->background_image) }}" 
                                             alt="Background Image" 
                                             class="img-thumbnail" 
                                             style="max-width: 300px; max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Service Meta -->
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info me-2"></i>
                        Service Meta
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label fw-bold">Created</label>
                        <p class="form-control-plaintext">{{ $service->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="form-group">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="form-control-plaintext">{{ $service->updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body d-grid gap-2">
                    <a href="{{ route('admin.services.edit', $service) }}" class="btn-admin btn-admin-primary">
                        <i class="fas fa-edit me-2"></i> Edit Service
                    </a>
                    <form action="{{ route('admin.services.destroy', $service) }}" 
                          method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-admin btn-admin-danger w-100">
                            <i class="fas fa-trash me-2"></i> Delete Service
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
