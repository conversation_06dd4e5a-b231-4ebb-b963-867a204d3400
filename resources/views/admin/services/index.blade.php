@extends('admin.layouts.app')

@section('title', 'Services Management')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Services</span>
    </div>
    <h1 class="page-title">Services Management</h1>
    <p class="page-subtitle">Manage your services and their display settings</p>
    <div class="page-actions">
        <a href="{{ route('admin.services.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Service
        </a>
    </div>
@endsection

@section('content')
    <div class="admin-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-cogs me-2"></i>All Services
            </h5>
        </div>
        <div class="card-body">
            @if($services->count() > 0)
                <div class="table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th width="60">#</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Background</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($services as $index => $service)
                                <tr>
                                    <td>
                                        <span class="text-muted fw-medium">{{ $index + 1 }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($service->icon_class)
                                                <i class="{{ $service->icon_class }} me-2" style="color: {{ $service->icon_color ?: '#007bff' }}"></i>
                                            @endif
                                            <strong>{{ $service->title }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        @if($service->category)
                                            <span class="badge badge-primary">{{ \App\Models\Service::getServiceCategories()[$service->category] ?? $service->category }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ \App\Models\Service::getServiceTypes()[$service->service_type] ?? $service->service_type }}</span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 250px;">
                                            {{ Str::limit($service->description, 80) }}
                                        </div>
                                    </td>
                                    <td>
                                        @if($service->background_type === 'color')
                                            <div class="d-flex align-items-center">
                                                <div class="color-preview me-2" 
                                                     style="width: 20px; height: 20px; background-color: {{ $service->background_color }}; border: 1px solid #ddd; border-radius: 3px;"></div>
                                                <span class="badge badge-info">Color</span>
                                            </div>
                                        @elseif($service->background_type === 'image')
                                            <span class="badge badge-success">Image</span>
                                        @else
                                            <span class="badge badge-secondary">None</span>
                                        @endif
                                    </td>

                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="{{ route('admin.services.show', $service) }}"
                                               class="btn-admin btn-admin-outline btn-sm"
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.services.edit', $service) }}"
                                               class="btn-admin btn-admin-primary btn-sm"
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.services.destroy', $service) }}" 
                                                  method="POST" 
                                                  class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to delete this service?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-admin btn-admin-danger btn-sm"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h5 class="empty-title">No Services Yet</h5>
                        <p class="empty-subtitle">Start by adding your first service to showcase what you offer.</p>
                        <a href="{{ route('admin.services.create') }}" class="btn-admin btn-admin-primary btn-admin-lg">
                            <i class="fas fa-plus me-2"></i>Add Your First Service
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
