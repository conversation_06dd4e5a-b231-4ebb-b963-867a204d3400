@extends('layouts.app')

@section('title', 'Our Projects - Hestia Abodes')
@section('description', 'Explore our premium residential and commercial projects across prime locations. Discover your perfect property with Hestia Abodes.')

@section('content')
<!-- Page Header -->
<section class="page-header" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title" style="color:white;">Our Projects</h1>
                <p class="page-subtitle">Discover Premium Properties Across Prime Locations</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Our Projects</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Project Filters -->
<section class="project-filters section-padding-sm">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filter-tabs text-center">
                    <button class="filter-btn active" data-filter="all">All Projects</button>
                    @foreach($projectTypes as $type)
                        <button class="filter-btn" data-filter="{{ strtolower(str_replace(' ', '-', $type)) }}">{{ $type }}</button>
                    @endforeach
                    <button class="filter-btn" data-filter="featured">Featured</button>
                    <button class="filter-btn" data-filter="ready_to_move">Ready to Move</button>
                    <button class="filter-btn" data-filter="upcoming">Upcoming</button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dynamic Projects Grid -->
<section class="projects-grid section-padding">
    <div class="container">
        @if($projects->count() > 0)
            <div class="row">
                @foreach($projects as $project)
                    <div class="col-lg-4 col-md-6 mb-4 project-item"
                         data-category="{{ strtolower(str_replace(' ', '-', $project->project_type)) }} {{ $project->status }} {{ $project->featured ? 'featured' : '' }}">
                        <div class="project-card">
                            <div class="project-image">
                                <img src="{{ asset('images/default-property.jpg') }}" alt="{{ $project->name }}" class="img-fluid">
                                <div class="project-overlay">
                                    <div class="project-status">
                                        @if($project->featured)
                                            <span class="badge bg-warning text-dark me-1">Featured</span>
                                        @endif
                                        {{ $project->project_type }}
                                    </div>
                                    <div class="project-actions">
                                        <a href="{{ route('project-details', $project->slug) }}" class="btn btn-primary btn-sm">View Details</a>
                                        <a href="{{ route('contact') }}" class="btn btn-outline-light btn-sm">Schedule Visit</a>
                                    </div>
                                </div>
                            </div>
                            <div class="project-content">
                                <h3 class="project-name">{{ $project->name }}</h3>
                                <p class="project-location">
                                    <i class="fas fa-map-marker-alt"></i> {{ $project->location }}, {{ $project->city }}
                                </p>
                                <div class="project-details">
                                    <div class="detail-item">
                                        <span class="detail-label">Configuration:</span>
                                        <span class="detail-value">{{ $project->property_types }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Possession:</span>
                                        <span class="detail-value">{{ $project->possession_date }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Price:</span>
                                        <span class="detail-value price">{{ $project->price_range }}</span>
                                    </div>
                                </div>
                                @if($project->short_description)
                                    <p class="project-description">{{ Str::limit($project->short_description, 100) }}</p>
                                @endif
                                <div class="project-features">
                                    <span class="feature-tag">{{ $project->developer }}</span>
                                    @if($project->status === 'ready_to_move')
                                        <span class="feature-tag bg-success">Ready to Move</span>
                                    @elseif($project->status === 'upcoming')
                                        <span class="feature-tag bg-info">Upcoming</span>
                                    @elseif($project->status === 'ongoing')
                                        <span class="feature-tag bg-warning">Ongoing</span>
                                    @endif
                                    @if($project->rera_id)
                                        <span class="feature-tag">RERA Approved</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- No Projects Found -->
            <div class="row">
                <div class="col-12 text-center py-5">
                    <div class="no-projects-found">
                        <i class="fas fa-building fa-4x text-muted mb-4"></i>
                        <h3 class="text-muted">No Projects Available</h3>
                        <p class="text-muted">We're working on bringing you exciting new projects. Please check back soon!</p>
                        <a href="{{ route('contact') }}" class="btn btn-primary">Get Notified About New Projects</a>
                    </div>
                </div>
            </div>
        @endif

    </div>
</section>

<!-- Call to Action -->
<section class="cta-section section-padding bg-primary">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="text-white mb-4">Can't Find What You're Looking For?</h2>
                <p class="text-white mb-4">Let our experts help you find the perfect property that matches your requirements and budget.</p>
                <a href="#contact" class="btn btn-light btn-lg me-3">Get Personalized Recommendations</a>
                <a href="tel:+919067881848" class="btn btn-outline-light btn-lg">Call Now: +91 ************</a>
            </div>
        </div>
    </div>
</section>
@endsection
