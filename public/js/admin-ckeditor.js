/**
 * Admin CKEditor Configuration
 * Latest CKEditor 5 with enhanced features for admin panels
 */

// Global CKEditor configuration
window.AdminCKEditorConfig = {
    toolbar: {
        items: [
            'heading', '|',
            'bold', 'italic', 'underline', 'strikethrough', '|',
            'fontSize', 'fontColor', 'fontBackgroundColor', '|',
            'alignment', '|',
            'numberedList', 'bulletedList', '|',
            'outdent', 'indent', '|',
            'link', 'blockQuote', 'insertTable', '|',
            'imageUpload', 'mediaEmbed', '|',
            'undo', 'redo', '|',
            'sourceEditing'
        ]
    },
    language: 'en',
    fontSize: {
        options: [
            9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36
        ]
    },
    fontColor: {
        colors: [
            {
                color: 'hsl(0, 0%, 0%)',
                label: 'Black'
            },
            {
                color: 'hsl(0, 0%, 30%)',
                label: 'Dim grey'
            },
            {
                color: 'hsl(0, 0%, 60%)',
                label: 'Grey'
            },
            {
                color: 'hsl(0, 0%, 90%)',
                label: 'Light grey'
            },
            {
                color: 'hsl(0, 0%, 100%)',
                label: 'White',
                hasBorder: true
            },
            {
                color: 'hsl(0, 75%, 60%)',
                label: 'Red'
            },
            {
                color: 'hsl(30, 75%, 60%)',
                label: 'Orange'
            },
            {
                color: 'hsl(60, 75%, 60%)',
                label: 'Yellow'
            },
            {
                color: 'hsl(90, 75%, 60%)',
                label: 'Light green'
            },
            {
                color: 'hsl(120, 75%, 60%)',
                label: 'Green'
            },
            {
                color: 'hsl(150, 75%, 60%)',
                label: 'Aquamarine'
            },
            {
                color: 'hsl(180, 75%, 60%)',
                label: 'Turquoise'
            },
            {
                color: 'hsl(210, 75%, 60%)',
                label: 'Light blue'
            },
            {
                color: 'hsl(240, 75%, 60%)',
                label: 'Blue'
            },
            {
                color: 'hsl(270, 75%, 60%)',
                label: 'Purple'
            }
        ]
    },
    fontBackgroundColor: {
        colors: [
            {
                color: 'hsl(0, 0%, 0%)',
                label: 'Black'
            },
            {
                color: 'hsl(0, 0%, 30%)',
                label: 'Dim grey'
            },
            {
                color: 'hsl(0, 0%, 60%)',
                label: 'Grey'
            },
            {
                color: 'hsl(0, 0%, 90%)',
                label: 'Light grey'
            },
            {
                color: 'hsl(0, 0%, 100%)',
                label: 'White',
                hasBorder: true
            },
            {
                color: 'hsl(0, 75%, 60%)',
                label: 'Red'
            },
            {
                color: 'hsl(30, 75%, 60%)',
                label: 'Orange'
            },
            {
                color: 'hsl(60, 75%, 60%)',
                label: 'Yellow'
            },
            {
                color: 'hsl(90, 75%, 60%)',
                label: 'Light green'
            },
            {
                color: 'hsl(120, 75%, 60%)',
                label: 'Green'
            },
            {
                color: 'hsl(150, 75%, 60%)',
                label: 'Aquamarine'
            },
            {
                color: 'hsl(180, 75%, 60%)',
                label: 'Turquoise'
            },
            {
                color: 'hsl(210, 75%, 60%)',
                label: 'Light blue'
            },
            {
                color: 'hsl(240, 75%, 60%)',
                label: 'Blue'
            },
            {
                color: 'hsl(270, 75%, 60%)',
                label: 'Purple'
            }
        ]
    },
    table: {
        contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells',
            'tableCellProperties',
            'tableProperties'
        ]
    },
    image: {
        toolbar: [
            'imageStyle:inline',
            'imageStyle:block',
            'imageStyle:side',
            '|',
            'toggleImageCaption',
            'imageTextAlternative'
        ]
    },
    link: {
        decorators: {
            openInNewTab: {
                mode: 'manual',
                label: 'Open in a new tab',
                attributes: {
                    target: '_blank',
                    rel: 'noopener noreferrer'
                }
            }
        }
    },
    heading: {
        options: [
            { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
            { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
            { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
            { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
            { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
        ]
    }
};

// Initialize CKEditor for a given selector
window.initAdminCKEditor = function(selector, customConfig = {}) {
    const config = { ...window.AdminCKEditorConfig, ...customConfig };
    
    return ClassicEditor
        .create(document.querySelector(selector), config)
        .then(editor => {
            console.log('CKEditor initialized successfully for:', selector);
            return editor;
        })
        .catch(error => {
            console.error('CKEditor initialization error for', selector, ':', error);
            throw error;
        });
};

// Initialize multiple CKEditor instances
window.initMultipleAdminCKEditors = function(selectors, customConfig = {}) {
    const promises = selectors.map(selector => {
        const element = document.querySelector(selector);
        if (element) {
            return window.initAdminCKEditor(selector, customConfig);
        } else {
            console.warn('Element not found for selector:', selector);
            return Promise.resolve(null);
        }
    });
    
    return Promise.all(promises);
};

// Auto-initialize CKEditor for common selectors
document.addEventListener('DOMContentLoaded', function() {
    // Common selectors for description fields
    const commonSelectors = [
        '#description',
        '.ckeditor',
        '[data-ckeditor="true"]'
    ];
    
    commonSelectors.forEach(selector => {
        const element = document.querySelector(selector);
        if (element && !element.hasAttribute('data-ckeditor-initialized')) {
            element.setAttribute('data-ckeditor-initialized', 'true');
            window.initAdminCKEditor(selector)
                .then(editor => {
                    // Store editor instance for later use
                    element.ckeditorInstance = editor;
                })
                .catch(error => {
                    console.error('Auto-initialization failed for', selector, ':', error);
                });
        }
    });
});
