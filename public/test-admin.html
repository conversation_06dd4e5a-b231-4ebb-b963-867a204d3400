<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Admin Functionality Test</h1>
        
        <!-- Test Content Type Dropdown -->
        <div class="mb-3">
            <label for="content_type" class="form-label">Content Type</label>
            <select class="form-select" id="content_type">
                <option value="">Select Content Type</option>
                <option value="text">Text Only</option>
                <option value="icon">With Icon</option>
                <option value="image">With Image</option>
            </select>
        </div>
        
        <!-- Icon Section -->
        <div id="icon-section" class="mb-3" style="display: none;">
            <label class="form-label">Icon Section</label>
            <div class="alert alert-info">Icon section is visible</div>
            <button type="button" class="btn btn-primary" id="icon-picker-btn">Browse Icons</button>
        </div>
        
        <!-- Image Section -->
        <div id="image-section" class="mb-3" style="display: none;">
            <label class="form-label">Image Section</label>
            <div class="alert alert-success">Image section is visible</div>
        </div>
        
        <!-- Add Item Test -->
        <div class="mb-3">
            <button type="button" class="btn btn-outline-primary" id="add-subcontent">
                <i class="fas fa-plus me-1"></i>Add Item
            </button>
        </div>
        
        <div id="subcontent-container">
            <!-- Items will be added here -->
        </div>
        
        <!-- Test Results -->
        <div class="mt-4">
            <h3>Test Results:</h3>
            <div id="test-results" class="alert alert-secondary">
                Tests will appear here...
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="iconPickerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Select Icon</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Icon picker modal is working!</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            let testResults = [];
            
            function addResult(test, passed) {
                testResults.push(`${test}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
                results.innerHTML = testResults.join('<br>');
            }
            
            // Test 1: Elements exist
            const contentTypeSelect = document.getElementById('content_type');
            const iconSection = document.getElementById('icon-section');
            const imageSection = document.getElementById('image-section');
            const addBtn = document.getElementById('add-subcontent');
            
            addResult('Content Type Select Found', !!contentTypeSelect);
            addResult('Icon Section Found', !!iconSection);
            addResult('Image Section Found', !!imageSection);
            addResult('Add Button Found', !!addBtn);
            
            // Test 2: Content type toggle
            function toggleContentSections() {
                const selectedType = contentTypeSelect.value;
                
                // Hide all sections
                iconSection.style.display = 'none';
                imageSection.style.display = 'none';
                
                // Show relevant sections
                if (selectedType === 'icon') {
                    iconSection.style.display = 'block';
                } else if (selectedType === 'image') {
                    imageSection.style.display = 'block';
                }
                
                addResult(`Content Type Toggle (${selectedType})`, true);
            }
            
            contentTypeSelect.addEventListener('change', toggleContentSections);
            addResult('Content Type Event Listener', true);
            
            // Test 3: Add Item functionality
            let itemCount = 0;
            function addSubcontentItem() {
                const container = document.getElementById('subcontent-container');
                const itemHtml = `
                    <div class="border rounded p-3 mb-3">
                        <h6>Item ${itemCount + 1}</h6>
                        <input type="text" class="form-control" placeholder="Title">
                    </div>
                `;
                container.insertAdjacentHTML('beforeend', itemHtml);
                itemCount++;
                addResult(`Add Item ${itemCount}`, true);
            }
            
            addBtn.addEventListener('click', addSubcontentItem);
            addResult('Add Button Event Listener', true);
            
            // Test 4: Bootstrap Modal
            const iconPickerBtn = document.getElementById('icon-picker-btn');
            let iconPickerModal = null;
            
            if (typeof bootstrap !== 'undefined') {
                iconPickerModal = new bootstrap.Modal(document.getElementById('iconPickerModal'));
                addResult('Bootstrap Modal Initialized', true);
                
                iconPickerBtn.addEventListener('click', function() {
                    iconPickerModal.show();
                    addResult('Modal Show', true);
                });
            } else {
                addResult('Bootstrap Available', false);
            }
            
            addResult('All Tests Completed', true);
        });
    </script>
</body>
</html>
