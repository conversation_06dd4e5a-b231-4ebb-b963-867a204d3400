<?php

// Simple script to add columns to about_us_sections table
$host = '127.0.0.1';
$port = '3306';
$dbname = 'hestia_abodes';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if columns already exist
    $stmt = $pdo->query("DESCRIBE about_us_sections");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $columnsToAdd = [
        'image_position' => "ALTER TABLE about_us_sections ADD COLUMN image_position VARCHAR(255) DEFAULT 'left' AFTER image_path",
        'columns_count' => "ALTER TABLE about_us_sections ADD COLUMN columns_count INT DEFAULT 1 AFTER image_position",
        'subcontent' => "ALTER TABLE about_us_sections ADD COLUMN subcontent JSON NULL AFTER columns_count",
        'background_type' => "ALTER TABLE about_us_sections ADD COLUMN background_type VARCHAR(255) DEFAULT 'none' AFTER subcontent",
        'background_color' => "ALTER TABLE about_us_sections ADD COLUMN background_color VARCHAR(255) NULL AFTER background_type",
        'background_image' => "ALTER TABLE about_us_sections ADD COLUMN background_image VARCHAR(255) NULL AFTER background_color",
        'icon_color' => "ALTER TABLE about_us_sections ADD COLUMN icon_color VARCHAR(255) DEFAULT '#ffffff' AFTER background_image"
    ];
    
    foreach ($columnsToAdd as $columnName => $sql) {
        if (!in_array($columnName, $columns)) {
            echo "Adding column: $columnName\n";
            $pdo->exec($sql);
            echo "Column $columnName added successfully\n";
        } else {
            echo "Column $columnName already exists\n";
        }
    }
    
    echo "Migration completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
